const coinpokerData = {
    "id": "coinpoker",
    "name": "CoinPoker",
    "environments": [
        {
            "section": "Development",
            "items": [
                {
                    "name": "Coinpoker Frontend",
                    "tag": "DEV",
                    "url": "https://coinpoker-frontend.dev.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-frontend",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "S3 Static Website",
                        "pipeline": "coinpoker-dev-frontend-pipeline",
                        "secrets": "/dev/app/coinpoker/frontend/env-vars",
                        "ecrRepo": "N/A"
                    }
                },
                {
                    "name": "Coinpoker Backend",
                    "tag": "DEV",
                    "url": "https://coinpoker-backend.dev.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-backend",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-backend-pipeline",
                        "secrets": "/dev/app/coinpoker/backend/env-vars",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-backend"
                    }
                },
                {
                    "name": "Coinpoker Update",
                    "tag": "DEV",
                    "url": "https://external-dev.coinpoker.com",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-web-client",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-update-pipeline",
                        "secrets": "/dev/app/coinpoker/update/env-vars",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-update"
                    }
                },
                {
                    "name": "Coinpoker Auth Session",
                    "tag": "DEV",
                    "url": "https://coinpoker-auth-session.dev.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-auth-session-signer",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-auth-session-pipeline",
                        "secrets": "/dev/app/coinpoker/auth-session/env-vars",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-auth-session"
                    }
                },
                {
                    "name": "Coinpoker Proxy Server",
                    "tag": "DEV",
                    "url": "https://coinpoker-proxy-server.dev.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-proxy",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-proxy-server-pipeline",
                        "secrets": "/dev/app/coinpoker/proxy-server/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-proxy-server"
                    }
                },
                {
                    "name": "Coinpoker CustomerIO Publisher",
                    "tag": "DEV",
                    "url": "https://coinpoker-customerio-publisher.dev.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-customerio-publisher",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-customerio-pub-pipeline",
                        "secrets": "/dev/app/coinpoker/customerio-publisher/cio.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-customerio-pub"
                    }
                },
                {
                    "name": "Coinpoker Poker API",
                    "tag": "DEV",
                    "url": "https://coinpoker-poker-api.dev.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-poker-api",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-poker-api-pipeline",
                        "secrets": "/dev/app/coinpoker/poker-api/env",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-api"
                    }
                },
                {
                    "name": "Coinpoker Poker Init",
                    "tag": "DEV",
                    "url": "https://coinpoker-poker-init.dev.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-poker-api",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-poker-init-pipeline",
                        "secrets": "/dev/app/coinpoker/poker-init/env",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-init"
                    }
                },
                {
                    "name": "Coinpoker Backend Public",
                    "tag": "DEV",
                    "url": "https://coinpoker-backend-public.dev.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-backend-public",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-backend-public-pipeline",
                        "secrets": "/dev/app/coinpoker/backend-public/env",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-backend-public"
                    }
                },
                {
                    "name": "Coinpoker OFCP Backoffice",
                    "tag": "DEV",
                    "url": "https://coinpoker-php-ofcp-backoffice.dev.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-php-ofcp-backoffice",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-ofcp-backoffice-pipeline",
                        "secrets": "/dev/app/coinpoker/php-ofcp-backoffice/backoffice.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-ofcp-backoffice"
                    }
                },
                {
                    "name": "Coinpoker Accounting",
                    "tag": "DEV",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-accounting",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-accounting-pipeline",
                        "secrets": "/dev/app/coinpoker/accounting/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-accounting"
                    }
                },
                {
                    "name": "Coinpoker Authentication",
                    "tag": "DEV",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-authentication",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-authentication-pipeline",
                        "secrets": "/dev/app/coinpoker/authentication/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-authentication"
                    }
                },
                {
                    "name": "Coinpoker Poker Operation",
                    "tag": "DEV",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-poker-operation",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-poker-operation-pipeline",
                        "secrets": "/dev/app/coinpoker/poker-operation/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-operation"
                    }
                },
                {
                    "name": "Coinpoker Server Lobby",
                    "tag": "DEV",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-lobby",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-server-lobby-pipeline",
                        "secrets": "/dev/app/coinpoker/server-lobby/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-server-lobby"
                    }
                },
                {
                    "name": "Coinpoker Poker Lobby",
                    "tag": "DEV",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-poker-lobby",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-poker-lobby-pipeline",
                        "secrets": "/dev/app/coinpoker/poker-lobby/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-lobby"
                    }
                },
                {
                    "name": "Coinpoker Server Statistics",
                    "tag": "DEV",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-statistics",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-server-statistics-pipeline",
                        "secrets": "/dev/app/coinpoker/server-statistics/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-server-statistics"
                    }
                },
                {
                    "name": "Coinpoker Poker History",
                    "tag": "DEV",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-poker-history",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-poker-history-pipeline",
                        "secrets": "/dev/app/coinpoker/server-poker-history/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-history"
                    }
                },
                {
                    "name": "Coinpoker Server Chat",
                    "tag": "DEV",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-chat",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-server-chat-pipeline",
                        "secrets": "/dev/app/coinpoker/server-chat/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-server-chat"
                    }
                },
                {
                    "name": "Coinpoker Backoffice multitenant",
                    "tag": "DEV",
                    "url": "https://coinpoker-backoffice-multitenant.dev.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "backoffice-multitenant-poc",
                        "branch": "develop"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-dev-backoffice-mt-pipeline",
                        "secrets": "/dev/app/coinpoker/backoffice-multitenant/env",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-backoffice-mt"
                    }
                }
            ]
        },
        {
            "section": "Staging",
            "items": [
                {
                    "name": "Coinpoker Update",
                    "tag": "STG",
                    "url": "https://coinpoker-update.stg.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-web-client",
                        "branch": "stg"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-update-pipeline",
                        "secrets": "/dev/app/coinpoker/update/env-vars",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-update"
                    }
                },
                {
                    "name": "Coinpoker Frontend",
                    "tag": "STG",
                    "url": "https://coinpoker-frontend.stg.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-frontend",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "S3 Static Website",
                        "pipeline": "coinpoker-stg-frontend-s-pipeline",
                        "secrets": "/stg/app/coinpoker/frontend/env-vars",
                        "ecrRepo": "N/A"
                    }
                },
                {
                    "name": "Coinpoker Backend",
                    "tag": "STG",
                    "url": "https://coinpoker-backend.stg.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-backend",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-backend-s-pipeline",
                        "secrets": "/stg/app/coinpoker/backend/env-vars",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-backend"
                    }
                },
                {
                    "name": "Coinpoker Update",
                    "tag": "STG",
                    "url": "https://external-stg.coinpoker.com",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-web-client",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-update-s-pipeline",
                        "secrets": "/stg/app/coinpoker/update/env-vars",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-update"
                    }
                },
                {
                    "name": "Coinpoker Auth Session",
                    "tag": "STG",
                    "url": "https://coinpoker-auth-session.stg.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-auth-session-signer",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-auth-session-s-pipeline",
                        "secrets": "/stg/app/coinpoker/auth-session/env-vars",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-auth-session"
                    }
                },
                {
                    "name": "Coinpoker Proxy Server",
                    "tag": "STG",
                    "url": "https://coinpoker-proxy-server.stg.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-proxy",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-proxy-server-s-pipeline",
                        "secrets": "/stg/app/coinpoker/proxy-server/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-proxy-server"
                    }
                },
                {
                    "name": "Coinpoker CustomerIO Publisher",
                    "tag": "STG",
                    "url": "https://coinpoker-customerio-publisher.stg.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-customerio-publisher",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-customerio-pub-s-pipeline",
                        "secrets": "/stg/app/coinpoker/customerio-publisher/cio.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-customerio-pub"
                    }
                },
                {
                    "name": "Coinpoker Poker API",
                    "tag": "STG",
                    "url": "https://coinpoker-poker-api.stg.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-poker-api",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-poker-api-s-pipeline",
                        "secrets": "/stg/app/coinpoker/poker-api/env",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-api"
                    }
                },
                {
                    "name": "Coinpoker Poker Init",
                    "tag": "STG",
                    "url": "https://coinpoker-poker-init.stg.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-poker-api",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-poker-init-s-pipeline",
                        "secrets": "/stg/app/coinpoker/poker-init/env",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-init"
                    }
                },
                {
                    "name": "Coinpoker Backend Public",
                    "tag": "STG",
                    "url": "https://coinpoker-backend-public.stg.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-backend-public",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-backend-public-s-pipeline",
                        "secrets": "/stg/app/coinpoker/backend-public/env",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-backend-public"
                    }
                },
                {
                    "name": "Coinpoker OFCP Backoffice",
                    "tag": "STG",
                    "url": "https://coinpoker-php-ofcp-backoffice.stg.coinplatform.io",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-php-ofcp-backoffice",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-ofcp-backoffice-s-pipeline",
                        "secrets": "/stg/app/coinpoker/php-ofcp-backoffice/backoffice.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-ofcp-backoffice"
                    }
                },
                {
                    "name": "Coinpoker Accounting",
                    "tag": "STG",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-accounting",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-accounting-s-pipeline",
                        "secrets": "/stg/app/coinpoker/accounting/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-accounting"
                    }
                },
                {
                    "name": "Coinpoker Authentication",
                    "tag": "STG",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-authentication",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-authentication-s-pipeline",
                        "secrets": "/stg/app/coinpoker/authentication/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-authentication"
                    }
                },
                {
                    "name": "Coinpoker Poker Operation",
                    "tag": "STG",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-poker-operation",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-poker-operation-s-pipeline",
                        "secrets": "/stg/app/coinpoker/poker-operation/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-operation"
                    }
                },
                {
                    "name": "Coinpoker Server Lobby",
                    "tag": "STG",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-lobby",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-server-lobby-s-pipeline",
                        "secrets": "/stg/app/coinpoker/server-lobby/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-server-lobby"
                    }
                },
                {
                    "name": "Coinpoker Poker Lobby",
                    "tag": "STG",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-poker-lobby",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-poker-lobby-s-pipeline",
                        "secrets": "/stg/app/coinpoker/poker-lobby/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-lobby"
                    }
                },
                {
                    "name": "Coinpoker Server Statistics",
                    "tag": "STG",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-statistics",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-server-statistics-s-pipeline",
                        "secrets": "/stg/app/coinpoker/server-statistics/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-server-statistics"
                    }
                },
                {
                    "name": "Coinpoker Poker History",
                    "tag": "STG",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-poker-history",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-poker-history-s-pipeline",
                        "secrets": "/stg/app/coinpoker/server-poker-history/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-history"
                    }
                },
                {
                    "name": "Coinpoker Server Chat",
                    "tag": "STG",
                    "url": "",
                    "github": {
                        "owner": "BlockLabsDev",
                        "repository": "coinpoker-server-chat",
                        "branch": "staging"
                    },
                    "aws": {
                        "account": "************",
                        "region": "Ireland (eu-west-1)",
                        "infrastructure": "ECS Fargate",
                        "pipeline": "coinpoker-stg-server-chat-s-pipeline",
                        "secrets": "/stg/app/coinpoker/server-chat/modules.conf",
                        "ecrRepo": "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-server-chat"
                    }
                }
            ]
        }
    ]
}