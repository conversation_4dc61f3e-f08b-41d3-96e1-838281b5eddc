# Multi-Tenant Laravel Project Makefile
# Description: Automates setup for multi-tenant Laravel application with Docker

.PHONY: help install build up down clean restart logs shell artisan migrate fresh seed seed-landlord seed-tenants tenants status hosts-info ssl-setup ssl-install ssl-certs ssl-clean setup-git-hooks

# Default port for the application
APP_PORT ?= 80

# Colors for output
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

# Default target
.DEFAULT_GOAL := help

## Show this help message
help:
	@echo "${GREEN}📋 Multi-Tenant Laravel Setup Commands${NC}"
	@echo ""
	@echo "🚀 ${YELLOW}Main Commands:${NC}"
	@echo "  make install    - Complete installation and setup"
	@echo "  make setup      - Setup existing containers (no rebuild)"
	@echo "  make build      - Build Docker containers"
	@echo "  make up         - Start containers"
	@echo "  make down       - Stop containers"
	@echo "  make restart    - Restart containers"
	@echo ""
	@echo "🔧 ${YELLOW}Development Commands:${NC}"
	@echo "  make shell      - Access Laravel container shell"
	@echo "  make logs       - Show container logs"
	@echo "  make artisan    - Run artisan commands (e.g., make artisan ARGS='migrate')"
	@echo "  make fresh      - Fresh installation (reset everything)"
	@echo "  make setup-git-hooks - Setup Git hooks from git-hooks/ directory"
	@echo ""
	@echo "🔒 ${YELLOW}SSL Commands:${NC}"
	@echo "  make ssl-setup  - Complete SSL setup (install mkcert + generate certs)"
	@echo "  make ssl-install- Install mkcert and setup local CA"
	@echo "  make ssl-certs  - Generate SSL certificates for domains"
	@echo "  make ssl-clean  - Remove generated SSL certificates"
	@echo ""
	@echo "🏢 ${YELLOW}Tenant Commands:${NC}"
	@echo "  make tenants    - Setup tenants"
	@echo "  make migrate    - Run tenant migrations"
	@echo "  make seed       - Seed databases (landlord + tenants)"
	@echo "  make seed-landlord - Seed only landlord database"
	@echo "  make seed-tenants  - Seed only tenant databases"
	@echo ""
	@echo "ℹ️  ${YELLOW}Info Commands:${NC}"
	@echo "  make status     - Show project status"
	@echo "  make hosts-info - Show hosts file configuration needed"

## Complete installation and setup
install: check-docker check-env install-composer-deps setup-git-hooks build up wait-for-db configure-env run-migrations create-tenant-dbs setup-tenants migrate-tenants seed success-message

## Check if Docker is running
check-docker:
	@echo "${YELLOW}🐳 Checking Docker...${NC}"
	@if ! docker info > /dev/null 2>&1; then \
		echo "${RED}❌ Docker is not running. Please start Docker first.${NC}"; \
		exit 1; \
	fi
	@echo "${GREEN}✅ Docker is running${NC}"

## Check if .env file exists, create from example if not
check-env:
	@echo "${YELLOW}⚙️  Checking environment configuration...${NC}"
	@if [ ! -f .env ]; then \
		if [ -f .env.example ]; then \
			cp .env.example .env; \
			echo "${GREEN}✅ Created .env from .env.example${NC}"; \
		else \
			echo "${RED}❌ No .env or .env.example file found${NC}"; \
			exit 1; \
		fi; \
	else \
		echo "${GREEN}✅ .env file exists${NC}"; \
	fi

## Install Composer dependencies using temporary container
install-composer-deps:
	@echo "${YELLOW}📦 Installing Composer dependencies...${NC}"
	@if [ ! -d "vendor/laravel/sail/runtimes" ]; then \
		echo "${YELLOW}🚀 Running composer install to get Laravel Sail...${NC}"; \
		docker run --rm -v $(PWD):/app -w /app composer:latest composer install --no-dev --optimize-autoloader --ignore-platform-req=ext-intl; \
	else \
		echo "${GREEN}✅ Composer dependencies already installed${NC}"; \
	fi

## Setup Git hooks
setup-git-hooks:
	@echo "${YELLOW}🔧 Setting up Git hooks...${NC}"
	@if [ -f "scripts/setup-git-hooks.sh" ]; then \
		./scripts/setup-git-hooks.sh; \
	else \
		echo "${YELLOW}⚠️  Git hooks setup script not found, skipping...${NC}"; \
	fi

## Build Docker containers
build:
	@echo "${YELLOW}🏗️  Building Docker containers...${NC}"
	docker-compose build #--no-cache
	@echo "${GREEN}✅ Docker containers built${NC}"

## Start containers
up:
	@echo "${YELLOW}🚀 Starting containers...${NC}"
	docker-compose up -d
	@echo "${GREEN}✅ Containers started${NC}"

## Stop containers
down:
	@echo "${YELLOW}🛑 Stopping containers...${NC}"
	docker-compose down
	@echo "${GREEN}✅ Containers stopped${NC}"

## Wait for database to be ready
wait-for-db:
	@echo "${YELLOW}⏳ Waiting for database to be ready...${NC}"
	@timeout=60; \
	while ! docker exec multi_tenant_mysql mysqladmin ping -h"localhost" --silent > /dev/null 2>&1; do \
		timeout=$$((timeout - 1)); \
		if [ $$timeout -eq 0 ]; then \
			echo "${RED}❌ Database failed to start within 60 seconds${NC}"; \
			exit 1; \
		fi; \
		sleep 1; \
	done
	@echo "${GREEN}✅ Database is ready${NC}"

## Configure environment
configure-env:
	@echo "${YELLOW}⚙️  Configuring environment...${NC}"
	@# Generate application key if not exists
	docker exec laravel_multitenancy php artisan key:generate --force
	@# Configure database and app settings
	docker exec laravel_multitenancy sed -i 's|APP_URL=.*|APP_URL=http://localhost:$(APP_PORT)|g' .env
	docker exec laravel_multitenancy sed -i 's|DB_CONNECTION=.*|DB_CONNECTION=mysql|g' .env
	docker exec laravel_multitenancy sed -i 's|DB_HOST=.*|DB_HOST=mysql|g' .env
	docker exec laravel_multitenancy sed -i 's|DB_PORT=.*|DB_PORT=3306|g' .env
	docker exec laravel_multitenancy sed -i 's|DB_DATABASE=.*|DB_DATABASE=laravel|g' .env
	docker exec laravel_multitenancy sed -i 's|DB_USERNAME=.*|DB_USERNAME=sail|g' .env
	docker exec laravel_multitenancy sed -i 's|DB_PASSWORD=.*|DB_PASSWORD=password|g' .env
	@# Set APP_PORT
	docker exec laravel_multitenancy sh -c 'grep -q "APP_PORT=" .env || echo "APP_PORT=$(APP_PORT)" >> .env'
	docker exec laravel_multitenancy sed -i 's|APP_PORT=.*|APP_PORT=$(APP_PORT)|g' .env
	@echo "${GREEN}✅ Environment configured${NC}"

## Run landlord migrations
run-migrations:
	@echo "${YELLOW}🗃️  Running landlord migrations...${NC}"
	docker exec laravel_multitenancy php artisan migrate --path=database/migrations/landlord --force
	@echo "${GREEN}✅ Landlord migrations completed${NC}"

## Create tenant databases
create-tenant-dbs:
	@echo "${YELLOW}🏢 Creating tenant databases...${NC}"
	docker exec multi_tenant_mysql mysql -u root -ppassword -e "\
		CREATE DATABASE IF NOT EXISTS coinpoker_db; \
		CREATE DATABASE IF NOT EXISTS coincasino_db; \
		GRANT ALL PRIVILEGES ON coinpoker_db.* TO 'sail'@'%'; \
		GRANT ALL PRIVILEGES ON coincasino_db.* TO 'sail'@'%'; \
		FLUSH PRIVILEGES;"
	@echo "${GREEN}✅ Tenant databases created (coinpoker_db, coincasino_db)${NC}"

## Setup tenants
setup-tenants:
	@echo "${YELLOW}🏢 Setting up tenants...${NC}"
	docker exec laravel_multitenancy php artisan tenants:setup
	@echo "${GREEN}✅ Tenants configured${NC}"

## Run tenant migrations
migrate-tenants:
	@echo "${YELLOW}🗃️  Running tenant migrations...${NC}"
	docker exec laravel_multitenancy php artisan tenant:artisan "migrate --force" --tenant=1
	docker exec laravel_multitenancy php artisan tenant:artisan "migrate --force" --tenant=2
	@echo "${GREEN}✅ Tenant migrations completed${NC}"

## Seed databases (landlord + tenants)
seed:
	@echo "${YELLOW}🌱 Seeding databases...${NC}"
	@echo "${YELLOW}📋 Seeding landlord database...${NC}"
	docker exec laravel_multitenancy php artisan db:seed --force
	@echo "${GREEN}✅ Database seeding completed${NC}"

## Seed only landlord database
seed-landlord:
	@echo "${YELLOW}📋 Seeding landlord database...${NC}"
	docker exec laravel_multitenancy php artisan db:seed --force
	@echo "${GREEN}✅ Landlord database seeded${NC}"

## Seed only tenant databases
seed-tenants:
	@echo "${YELLOW}🏢 Note: Tenant seeding command has been removed${NC}"
	@echo "${YELLOW}Use Laravel's built-in seeding or custom tenant migrations instead${NC}"

## Success message with instructions
success-message:
	@echo ""
	@echo "${GREEN}🎉 Installation completed successfully!${NC}"
	@echo ""
	@echo "${YELLOW}📋 Next steps:${NC}"
	@echo "1. Add these entries to your /etc/hosts file:"
	@echo "   ${GREEN}127.0.0.1 coinpoker.dev${NC}"
	@echo "   ${GREEN}127.0.0.1 coincasino.dev${NC}"
	@echo ""
	@echo "2. Access your applications:"
	@echo "   🎰 CoinPoker: ${GREEN}http://coinpoker.dev:$(APP_PORT)${NC}"
	@echo "   🎲 CoinCasino: ${GREEN}http://coincasino.dev:$(APP_PORT)${NC}"
	@echo ""
	@echo "3. Additional commands:"
	@echo "   - View logs: ${GREEN}make logs${NC}"
	@echo "   - Access shell: ${GREEN}make shell${NC}"
	@echo "   - Stop containers: ${GREEN}make down${NC}"
	@echo ""

## Access Laravel container shell
shell:
	@echo "${YELLOW}🐚 Accessing Laravel container shell...${NC}"
	docker exec -it laravel_multitenancy bash

## Show container logs
logs:
	@echo "${YELLOW}📋 Showing container logs...${NC}"
	docker-compose logs -f

## Run artisan commands
artisan:
	@if [ -z "$(ARGS)" ]; then \
		echo "${YELLOW}Usage: make artisan ARGS='command'${NC}"; \
		echo "Example: make artisan ARGS='migrate'"; \
	else \
		docker exec laravel_multitenancy php artisan $(ARGS); \
	fi

## Run migrations
migrate:
	@echo "${YELLOW}🗃️  Running migrations for all tenants...${NC}"
	docker exec laravel_multitenancy php artisan tenant:artisan "migrate" --tenant=1
	docker exec laravel_multitenancy php artisan tenant:artisan "migrate" --tenant=2
	@echo "${GREEN}✅ Migrations completed${NC}"

## Fresh installation (reset everything)
fresh: down clean build install

## Clean up everything
clean:
	@echo "${YELLOW}🧹 Cleaning up project containers and volumes...${NC}"
	@# Stop and remove containers, networks, and volumes for this project only
	docker-compose down -v --remove-orphans
	@# Remove any dangling volumes specific to this project
	@echo "${YELLOW}🗑️  Removing project-specific volumes...${NC}"
	@docker volume ls -q --filter "name=multi-tenant" | xargs -r docker volume rm 2>/dev/null || true
	@echo "${GREEN}✅ Project cleanup completed (other projects and images preserved)${NC}"

## Restart containers
restart: down up

## Show project status
status:
	@echo "${YELLOW}📊 Project Status${NC}"
	@echo ""
	@echo "🐳 ${YELLOW}Docker Containers:${NC}"
	@docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=laravel\|mysql\|nginx"
	@echo ""
	@echo "🌐 ${YELLOW}Application URLs:${NC}"
	@echo "  CoinPoker: http://coinpoker.dev:$(APP_PORT)"
	@echo "  CoinCasino: http://coincasino.dev:$(APP_PORT)"
	@echo ""
	@echo "📁 ${YELLOW}Project Directory:${NC} $(PWD)"

## Show hosts file configuration info
hosts-info:
	@echo "${YELLOW}📝 Hosts File Configuration${NC}"
	@echo ""
	@echo "Add the following lines to your /etc/hosts file:"
	@echo ""
	@echo "${GREEN}127.0.0.1 coinpoker.dev${NC}"
	@echo "${GREEN}127.0.0.1 coincasino.dev${NC}"
	@echo ""
	@echo "💡 ${YELLOW}How to edit hosts file:${NC}"
	@echo "  macOS/Linux: sudo nano /etc/hosts"
	@echo "  Windows: Edit C:\\Windows\\System32\\drivers\\etc\\hosts as Administrator"
	@echo ""

## Setup tenants only
tenants: setup-tenants migrate-tenants

## Complete SSL setup with mkcert
ssl-setup: ssl-install ssl-certs

## Install mkcert and setup local CA
ssl-install:
	@echo "${YELLOW}🔒 Setting up mkcert...${NC}"
	@# Check if mkcert is installed
	@if ! command -v mkcert > /dev/null 2>&1; then \
		echo "${YELLOW}📦 Installing mkcert...${NC}"; \
		if command -v brew > /dev/null 2>&1; then \
			brew install mkcert; \
		else \
			echo "${RED}❌ Homebrew not found. Please install mkcert manually.${NC}"; \
			echo "Visit: https://github.com/FiloSottile/mkcert"; \
			exit 1; \
		fi; \
	else \
		echo "${GREEN}✅ mkcert is already installed${NC}"; \
	fi
	@# Install local CA
	@echo "${YELLOW}🔑 Installing local Certificate Authority...${NC}"
	mkcert -install
	@echo "${GREEN}✅ Local CA installed${NC}"

## Generate SSL certificates for domains
ssl-certs:
	@echo "${YELLOW}🔒 Generating SSL certificates...${NC}"
	@# Create SSL directory if it doesn't exist
	@mkdir -p docker/nginx/ssl
	@# Generate certificates for coinpoker.dev
	@echo "${YELLOW}📜 Generating certificate for coinpoker.dev...${NC}"
	mkcert -key-file docker/nginx/ssl/coinpoker.dev.key -cert-file docker/nginx/ssl/coinpoker.dev.crt coinpoker.dev
	@# Generate certificates for coincasino.dev
	@echo "${YELLOW}📜 Generating certificate for coincasino.dev...${NC}"
	mkcert -key-file docker/nginx/ssl/coincasino.dev.key -cert-file docker/nginx/ssl/coincasino.dev.crt coincasino.dev
	@echo "${GREEN}✅ SSL certificates generated${NC}"
	@echo ""
	@echo "${YELLOW}📋 Certificate files created:${NC}"
	@echo "  🎰 CoinPoker: docker/nginx/ssl/coinpoker.dev.{crt,key}"
	@echo "  🎲 CoinCasino: docker/nginx/ssl/coincasino.dev.{crt,key}"
	@echo ""
	@echo "${YELLOW}💡 Remember to update your /etc/hosts file:${NC}"
	@echo "  ${GREEN}127.0.0.1 coinpoker.dev${NC}"
	@echo "  ${GREEN}127.0.0.1 coincasino.dev${NC}"

## Remove generated SSL certificates
ssl-clean:
	@echo "${YELLOW}🧹 Removing SSL certificates...${NC}"
	@rm -f docker/nginx/ssl/coinpoker.dev.crt docker/nginx/ssl/coinpoker.dev.key
	@rm -f docker/nginx/ssl/coincasino.dev.crt docker/nginx/ssl/coincasino.dev.key
	@echo "${GREEN}✅ SSL certificates removed${NC}"

## Setup existing containers (no rebuild)
setup: setup-git-hooks up wait-for-db configure-env run-migrations create-tenant-dbs setup-tenants migrate-tenants seed success-message
