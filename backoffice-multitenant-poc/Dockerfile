FROM shinsenter/php:8.4-fpm-nginx

# Set working directory
WORKDIR /var/www/html

# Install additional tools
RUN apt-get update && apt-get install -y \
    git \
    unzip \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# Install required PHP extensions
# Popular extensions already included: bcmath, gd, intl, pdo_mysql, pdo_pgsql, redis, zip, etc.
RUN phpaddmod mongodb imagick

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/bin/ --filename=composer

# Install global npm packages (optional)
RUN npm install -g pnpm

# Copy application files
COPY --chown=www-data:www-data . .

# Install PHP dependencies (ignore MongoDB if not needed in container)
RUN composer install --no-dev --optimize-autoloader --ignore-platform-req=ext-mongodb

# Install and build frontend assets (if needed)
RUN npm install && npm run build

# Set proper permissions
RUN chown -R www-data:www-data \
    /var/www/html/*

EXPOSE 80