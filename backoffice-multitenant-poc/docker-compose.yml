services:
    nginx:
        image: 'nginx:alpine'
        container_name: 'laravel_nginx'
        ports:
            - '${APP_PORT:-80}:80'
            - '443:443'
        volumes:
            - '.:/var/www/html'
            - './docker/nginx/default.conf:/etc/nginx/conf.d/default.conf'
            - './docker/nginx/ssl:/etc/nginx/ssl'
        networks:
            - sail
        depends_on:
            - laravel.test

    laravel.test:
        build:
            context: './vendor/laravel/sail/runtimes/8.4'
            dockerfile: Dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
        image: 'sail-8.4/app'
        container_name: 'laravel_multitenancy'
        extra_hosts:
            - 'host.docker.internal:host-gateway'
            - 'coinpoker.dev:127.0.0.1'
            - 'coincasino.dev:127.0.0.1'
        ports:
            - '${VITE_PORT:-5173}:${VITE_PORT:-5173}'
        environment:
            WWWUSER: '${WWWUSER}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
            IGNITION_LOCAL_SITES_PATH: '${PWD}'
            # Multi-tenancy environment variables
            TENANT_DOMAINS: 'coinpoker.dev,coincasino.dev'
        volumes:
            - '.:/var/www/html'
        networks:
            - sail
        depends_on:
            - mysql
    mysql:
        image: 'mysql/mysql-server:8.0'
        container_name: 'multi_tenant_mysql'
        ports:
            - '${FORWARD_DB_PORT:-3306}:3306'
        environment:
            MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ROOT_HOST: '%'
            MYSQL_DATABASE: '${DB_DATABASE}'
            MYSQL_USER: '${DB_USERNAME}'
            MYSQL_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ALLOW_EMPTY_PASSWORD: 1
        volumes:
            - 'sail-mysql:/var/lib/mysql'
            - './vendor/laravel/sail/database/mysql/create-testing-database.sh:/docker-entrypoint-initdb.d/10-create-testing-database.sh'
        networks:
            - sail
        healthcheck:
            test: ["CMD", "mysqladmin", "ping", "-p${DB_PASSWORD}"]
            retries: 3
            timeout: 5s
    mongodb:
        image: 'mongo:7.0'
        container_name: 'multi_tenant_mongodb'
        ports:
            - '${FORWARD_MONGO_PORT:-27017}:27017'
        environment:
            MONGO_INITDB_ROOT_USERNAME: '${MONGO_USERNAME:-root}'
            MONGO_INITDB_ROOT_PASSWORD: '${MONGO_PASSWORD:-password}'
        volumes:
            - 'sail-mongodb:/data/db'
        networks:
            - sail
        healthcheck:
            test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
            retries: 3
            timeout: 5s
networks:
    sail:
        driver: bridge
volumes:
    sail-mysql:
        driver: local
    sail-mongodb:
        driver: local
