# MultiTenant BackOffice POC

## Current Tenants
- coinpoker.dev
- coincasino.dev

## 📦 Used Packages
### Core Multi-Tenancy
- [spatie/laravel-multitenancy](https://github.com/spatie/laravel-multitenancy) - Multi-tenancy package for <PERSON><PERSON>
- [spatie/laravel-permission](https://github.com/spatie/laravel-permission) - Role and Permission management for Laravel
### Other Key Packages
- [filament/filament](https://filamentphp.com/) - Admin panel and form builder
- [laravel/fortify](https://laravel.com/docs/fortify) - Authentication backend
- [laravel/sanctum](https://laravel.com/docs/sanctum) - API authentication
- [calebporzio/sushi](https://github.com/calebporzio/sushi) - Eloquent's missing "array" driver

## Multi-Tenant Architecture

### Current Implementation: Spatie Laravel Multitenancy
We are using [<PERSON><PERSON>'s Lara<PERSON> Multitenancy](https://github.com/spatie/laravel-multitenancy) package, which provides:
- Database separation per tenant
- Automatic tenant identification through middleware (TenantAwareMiddleware::class)
- Tenant-specific configurations
- Easy tenant switching

### Alternative: Tenancy for Laravel (Open discussion)
[Tenancy for Laravel](https://tenancyforlaravel.com/) is another popular option that offers:
- Both single and multi-database tenancy
- Automatic tenant identification
- Event-based architecture
- Great integration with other packages

## User Permissions
The application implements role-based access control with the the package `spatie/laravel-permission` and following this basic structure of roles and permissions for the demo:

### Superadmin
- Has access to all permissions
- Can manage all tenants
- Can login in any tenant with the same session

### Backoffice General Roles
**[SuperAdmin] Permissions**
  - `coincasino_users_section_read`
  - `coincasino_users_section_write`
  - `coinpoker_users_section_read`
  - `coinpoker_users_section_write`

### Coincasino Roles
**[Admin Coincasino] permissions**
  - `coincasino_users_section_read`
  - `coincasino_users_section_write`

**[Viewer Coincasino] permissions**
  - `coincasino_users_section_read`

### Coinpoker Roles
**[Admin Coinpoker] permissions**
   - `coinpoker_users_section_read`
   - `coinpoker_users_section_write`

**[Viewer Coinpoker] permissions**
   - `coinpoker_users_section_read`

## User Data Implementation
The `ApiUser` model uses the [Sushi](https://github.com/calebporzio/sushi) package to provide an Eloquent-like interface for array data. This implementation:

### Current Features
- At the moment simulates fetching data from a remote (database or API).


