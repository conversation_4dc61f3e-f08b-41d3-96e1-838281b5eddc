<?php

return [

    /*
    |--------------------------------------------------------------------------
    | CoinPoker Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration for CoinPoker data sources and strategies
    |
    */

    'players' => [
        /*
        |--------------------------------------------------------------------------
        | Player Data Strategy
        |--------------------------------------------------------------------------
        |
        | Current strategy: 'legacy' (uses multiple databases + MongoDB + RabbitMQ)
        | Future strategy: 'api' (uses external API calls)
        |
        */
        'strategy' => env('COINPOKER_PLAYERS_STRATEGY', 'legacy'),

        /*
        |--------------------------------------------------------------------------
        | Legacy Strategy Configuration
        |--------------------------------------------------------------------------
        |
        | Configuration for the legacy multi-source data aggregation strategy
        |
        */
        'legacy' => [
            'connections' => [
                'coin_poker' => 'coinpoker.coin_poker',
                'gaming_system' => 'coinpoker.gaming_system',
                'ofcp' => 'coinpoker.ofcp',
            ],
            'mongodb' => [
                'enabled' => env('COINPOKER_MONGODB_ENABLED', true),
                'connection' => 'mongodb',
            ],
            'rabbitmq' => [
                'enabled' => env('COINPOKER_RABBITMQ_ENABLED', true),
                'host' => env('RABBITMQ_HOST', 'localhost'),
                'port' => env('RABBITMQ_PORT', 5672),
                'username' => env('RABBITMQ_USERNAME', 'guest'),
                'password' => env('RABBITMQ_PASSWORD', 'guest'),
                'queue' => env('COINPOKER_RABBITMQ_QUEUE', 'player_events'),
            ],
        ],

        /*
        |--------------------------------------------------------------------------
        | API Strategy Configuration (Future)
        |--------------------------------------------------------------------------
        |
        | Configuration for the future API-based strategy
        |
        */
        'api' => [
            'url' => env('COINPOKER_PLAYERS_API_URL'),
            'token' => env('COINPOKER_PLAYERS_API_TOKEN'),
            'timeout' => env('COINPOKER_PLAYERS_API_TIMEOUT', 30),
            'retry_attempts' => env('COINPOKER_PLAYERS_API_RETRY_ATTEMPTS', 3),
            'retry_delay' => env('COINPOKER_PLAYERS_API_RETRY_DELAY', 1000), // milliseconds
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Tournaments Configuration (Future)
    |--------------------------------------------------------------------------
    |
    | Configuration for tournament data sources and strategies
    |
    */
    'tournaments' => [
        'strategy' => env('COINPOKER_TOURNAMENTS_STRATEGY', 'legacy'),

        'legacy' => [
            'connections' => [
                'coin_poker' => 'coinpoker.coin_poker',
                'gaming_system' => 'coinpoker.gaming_system',
                'ofcp' => 'coinpoker.ofcp',
            ],
        ],

        'api' => [
            'url' => env('COINPOKER_TOURNAMENTS_API_URL'),
            'token' => env('COINPOKER_TOURNAMENTS_API_TOKEN'),
            'timeout' => env('COINPOKER_TOURNAMENTS_API_TIMEOUT', 30),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Heatmap Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for heatmap image generation
    |
    */
    'heatmap' => [
        'binary_path' => storage_path('app/heatmap/bin/out'),
        'backgrounds_path' => storage_path('app/heatmap/backgrounds'),
        'temp_path' => sys_get_temp_dir(),
        'cache_ttl' => env('COINPOKER_HEATMAP_CACHE_TTL', 3600), // 1 hour
        'enabled' => env('COINPOKER_HEATMAP_ENABLED', true),
        'php_generator' => [
            'heat_radius' => env('COINPOKER_HEATMAP_RADIUS', 30),
            'intensity_threshold' => env('COINPOKER_HEATMAP_THRESHOLD', 0.1),
            'overlay_opacity' => env('COINPOKER_HEATMAP_OPACITY', 60), // 0-100
        ],
    ],

];
