<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_REDIRECT_URI', '/auth/google/callback'),
        'hosted_domain' => env('GOOGLE_WORKSPACE_DOMAIN'), // For workspace domain restriction
    ],

    'coinpoker' => [
        'coinpoker_api' => [
            'api_url' => env('COINPOKER_COINPOKER_API_URL', 'http://dev.bo.coinpoker.com:5014/api/v1/admin'),
            'api_token' => env('COINPOKER_COINPOKER_API_TOKEN'),
        ],
        'accounting_api' => [
            'api_url' => env('COINPOKER_ACCOUNTING_API_URL'),
            'api_token' => env('COINPOKER_ACCOUNTING_API_KEY'),
        ],
        'poker_api' => [
            'api_url' => env('COINPOKER_POKER_API_URL'),
            'api_token' => env('COINPOKER_POKER_API_TOKEN'),
        ],
    ],

    'mongodb' => [
        'hand_log_uri' => env('MONGO_HAND_LOG_URI'),
        'options' => [
            'connectTimeoutMS' => 5000,
            'serverSelectionTimeoutMS' => 5000,
            'authSource' => 'poker',
        ],
    ],

];
