#!/bin/bash

# Setup Git Hooks Script
# This script copies git hooks from git-hooks/ directory to .git/hooks/

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}🔧 Setting up Git hooks...${NC}"

# Check if .git directory exists
if [ ! -d ".git" ]; then
    echo -e "${RED}❌ .git directory not found. This script must be run from a git repository.${NC}"
    exit 1
fi

# Check if git-hooks directory exists
if [ ! -d "git-hooks" ]; then
    echo -e "${YELLOW}⚠️  git-hooks directory not found. Creating it...${NC}"
    mkdir -p git-hooks
fi

# Create .git/hooks directory if it doesn't exist
mkdir -p .git/hooks

# Copy all hooks from git-hooks/ to .git/hooks/
if [ -d "git-hooks" ] && [ "$(ls -A git-hooks 2>/dev/null)" ]; then
    echo -e "${YELLOW}📋 Copying git hooks...${NC}"
    
    for hook in git-hooks/*; do
        if [ -f "$hook" ]; then
            hook_name=$(basename "$hook")
            cp "$hook" ".git/hooks/$hook_name"
            chmod +x ".git/hooks/$hook_name"
            echo -e "${GREEN}✅ Copied and made executable: $hook_name${NC}"
        fi
    done
    
    echo -e "${GREEN}🎉 Git hooks setup completed!${NC}"
else
    echo -e "${YELLOW}ℹ️  No git hooks found in git-hooks/ directory${NC}"
fi 