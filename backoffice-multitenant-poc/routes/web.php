<?php

use App\BackOffice\CoinPoker\Actions\GenerateHeatmapImageAction;
use App\Http\Controllers\Auth\GoogleAuthController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('filament.admin.auth.login');
});

Route::get('login', function () {
    return redirect()->route('filament.admin.auth.login');
});
Route::get('register', function () {
    return redirect()->route('filament.admin.auth.login');
});

// Google OAuth Routes
Route::get('/auth/google/redirect', [GoogleAuthController::class, 'redirect'])->name('google.redirect');
Route::get('/auth/google/callback', [GoogleAuthController::class, 'callback'])->name('google.callback');

// @todo: Remove this and use \App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages\PlayerHeatmapDetails::generateHeatmap
Route::get('/players/players_tracking/heatmap/{playerId}/generate/{heatmapId}', function (int $playerId, int $heatmapId) {
    return app(GenerateHeatmapImageAction::class)->execute($heatmapId);
})->name('players.heatmap.generate')->where(['playerId' => '[0-9]+', 'heatmapId' => '[0-9]+']);
