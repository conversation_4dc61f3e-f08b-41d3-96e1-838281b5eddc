APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

APP_PORT=80

GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_REDIRECT_URI=
GOOGLE_WORKSPACE_DOMAIN=

# XDebug Configuration
SAIL_XDEBUG_MODE=debug
SAIL_XDEBUG_CONFIG="client_host=host.docker.internal start_with_request=yes"

# Docker Sail Configuration
WWWGROUP=1000
WWWUSER=1000

# CURRENT DRUPAL DB HOST
COINPOKER_DB_HOST=**************
COINPOKER_DB_PORT=3306
COINPOKER_DB_USER=
COINPOKER_DB_PASSWORD=

# DATABASE NAMES
COINPOKER_COINPOKER_DB_NAME="coin_poker" # DRUPAL COINPOKER DB
COINPOKER_GAMING_SYSTEM_DB_NAME="gaming_system" # DRUPAL GAMING_SYSTEM DB
COINPOKER_OFCP_DB_NAME="ofcp" # DRUPAL OFCP DB

# DRUPAL COINPOKER API
COINPOKER_COINPOKER_API_URL=http://**************:5014/api/v1
COINPOKER_COINPOKER_API_TOKEN=
# DRUPAL GAMING_SYSTEM API
COINPOKER_POKER_API_URL=http://**************:5001/api/v1
COINPOKER_POKER_API_TOKEN=

# MongoDB Hand Log Configuration
# MONGO DEV
MONGO_HAND_LOG_URI=
MONGO_USERNAME=
MONGO_PASSWORD=
FORWARD_MONGO_PORT=

AMQP_HOST=
AMQP_USER=
AMQP_PASSWORD=

AMQP_CIO_HOST=
AMQP_CIO_USER=
AMQP_CIO_PASSWORD=

LEGACY_WEB_API_KEY=
ACCOUNTING_API_KEY=
ACCOUNTING_AFF_API_KEY=
ACCOUNTING_DASHFX_API_KEY=

# Ignition Configuration for PhpStorm
IGNITION_EDITOR=
IGNITION_REMOTE_SITES_PATH=
IGNITION_LOCAL_SITES_PATH=

MONGO_HAND_LOG_URI=