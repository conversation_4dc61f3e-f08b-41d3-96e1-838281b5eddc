# HTTP to HTTPS redirect
server {
    listen 80;
    server_name coinpoker.dev coincasino.dev localhost;
    return 301 https://$server_name$request_uri;
}

# HTTPS server for coinpoker.dev
server {
    listen 443 ssl http2;
    server_name coinpoker.dev;

    ssl_certificate /etc/nginx/ssl/coinpoker.dev.crt;
    ssl_certificate_key /etc/nginx/ssl/coinpoker.dev.key;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    location / {
        proxy_pass http://laravel.test:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Forwarded-Port 443;
        proxy_set_header X-Forwarded-Host $host;
        proxy_redirect off;
    }
}

# HTTPS server for coincasino.dev
server {
    listen 443 ssl http2;
    server_name coincasino.dev;

    ssl_certificate /etc/nginx/ssl/coincasino.dev.crt;
    ssl_certificate_key /etc/nginx/ssl/coincasino.dev.key;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    location / {
        proxy_pass http://laravel.test:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header X-Forwarded-Port 443;
        proxy_set_header X-Forwarded-Host $host;
        proxy_redirect off;
    }
}

# HTTP server for localhost (development)
server {
    listen 80;
    server_name localhost;

    location / {
        proxy_pass http://laravel.test:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
