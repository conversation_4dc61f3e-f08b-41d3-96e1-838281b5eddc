<?php

namespace App\Filament\Resources\ApiUserResource\Pages;

use App\Filament\Resources\ApiUserResource;
use App\Services\TenantPermissionService;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Http\RedirectResponse;

class CreateApiUser extends CreateRecord
{
    protected static string $resource = ApiUserResource::class;

    public function mount(): void
    {
        // Check if user has write permissions for current tenant
        if (!TenantPermissionService::canWrite()) {
            abort(403, 'You do not have permission to create users for this tenant.');
        }

        parent::mount();
    }

    public function getTitle(): string
    {
        $tenantName = TenantPermissionService::getTenantName();
        return "Create User ({$tenantName})";
    }
}
