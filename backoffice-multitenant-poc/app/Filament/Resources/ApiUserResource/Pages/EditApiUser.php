<?php

namespace App\Filament\Resources\ApiUserResource\Pages;

use App\Filament\Resources\ApiUserResource;
use App\Services\TenantPermissionService;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class Edit<PERSON>piUser extends EditRecord
{
    protected static string $resource = ApiUserResource::class;

    public function mount(int | string $record): void
    {
        // Check if user has write permissions for current tenant
        if (!TenantPermissionService::canWrite()) {
            abort(403, 'You do not have permission to edit users for this tenant.');
        }

        parent::mount($record);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make()
                ->visible(fn () => TenantPermissionService::canWrite()),
        ];
    }

    public function getTitle(): string
    {
        $tenantName = TenantPermissionService::getTenantName();
        return "Edit User ({$tenantName})";
    }
}
