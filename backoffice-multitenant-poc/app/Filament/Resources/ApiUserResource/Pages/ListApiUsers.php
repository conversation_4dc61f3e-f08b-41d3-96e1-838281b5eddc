<?php

namespace App\Filament\Resources\ApiUserResource\Pages;

use App\Filament\Resources\ApiUserResource;
use App\Services\TenantPermissionService;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListApiUsers extends ListRecords
{
    protected static string $resource = ApiUserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->visible(fn () => TenantPermissionService::canWrite()),
        ];
    }

    public function getTitle(): string
    {
        $tenantName = TenantPermissionService::getTenantName();
        return "Users ({$tenantName})";
    }

    protected function getHeaderWidgets(): array
    {
        return [
            // You can add widgets here later if needed
        ];
    }
}
