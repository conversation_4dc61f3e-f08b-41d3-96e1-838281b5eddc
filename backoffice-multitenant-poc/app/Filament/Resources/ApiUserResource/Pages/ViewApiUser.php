<?php

namespace App\Filament\Resources\ApiUserResource\Pages;

use App\Filament\Resources\ApiUserResource;
use App\Services\TenantPermissionService;
use Filament\Actions;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;

class ViewApiUser extends ViewRecord
{
    protected static string $resource = ApiUserResource::class;

    public function mount(int | string $record): void
    {
        // Check if user has read permissions for current tenant
        if (!TenantPermissionService::canRead()) {
            abort(403, 'You do not have permission to view users for this tenant.');
        }

        parent::mount($record);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn () => TenantPermissionService::canWrite()),
        ];
    }

    public function getTitle(): string
    {
        $tenantName = TenantPermissionService::getTenantName();
        return "View User ({$tenantName})";
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('User Profile')
                    ->schema([
                        Infolists\Components\Split::make([
                            Infolists\Components\Grid::make(2)
                                ->schema([
                                    Infolists\Components\ImageEntry::make('avatar')
                                        ->hiddenLabel()
                                        ->circular()
                                        ->defaultImageUrl(function ($record) {
                                            return 'https://api.dicebear.com/7.x/avataaars/svg?seed=' . urlencode($record->name);
                                        })
                                        ->size(120),
                                    
                                    Infolists\Components\Group::make([
                                        Infolists\Components\TextEntry::make('name')
                                            ->size(Infolists\Components\TextEntry\TextEntrySize::Large)
                                            ->weight('bold'),
                                        
                                        Infolists\Components\TextEntry::make('username')
                                            ->badge()
                                            ->color('gray')
                                            ->prefix('@'),
                                        
                                        Infolists\Components\TextEntry::make('status')
                                            ->badge()
                                            ->color(fn (string $state): string => match ($state) {
                                                'active' => 'success',
                                                'inactive' => 'danger',
                                                'pending' => 'warning',
                                                default => 'gray',
                                            }),
                                    ]),
                                ]),
                        ]),
                    ])
                    ->columns(1),

                Infolists\Components\Section::make('Contact Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('email')
                            ->icon('heroicon-m-envelope')
                            ->copyable()
                            ->color('primary'),
                        
                        Infolists\Components\TextEntry::make('phone')
                            ->icon('heroicon-m-phone')
                            ->copyable(),
                        
                        Infolists\Components\TextEntry::make('website')
                            ->icon('heroicon-m-globe-alt')
                            ->url(fn ($record) => $record->website)
                            ->openUrlInNewTab()
                            ->color('primary'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Company & Location')
                    ->schema([
                        Infolists\Components\TextEntry::make('company')
                            ->icon('heroicon-m-building-office-2'),
                        
                        Infolists\Components\Group::make([
                            Infolists\Components\TextEntry::make('address')
                                ->icon('heroicon-m-map-pin'),
                            
                            Infolists\Components\TextEntry::make('city'),
                            
                            Infolists\Components\TextEntry::make('zipcode'),
                        ])
                        ->columns(3),
                    ])
                    ->columns(1),

                Infolists\Components\Section::make('Timestamps')
                    ->schema([
                        Infolists\Components\TextEntry::make('created_at')
                            ->dateTime()
                            ->icon('heroicon-m-calendar-days'),
                        
                        Infolists\Components\TextEntry::make('updated_at')
                            ->dateTime()
                            ->icon('heroicon-m-calendar-days'),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }
}
