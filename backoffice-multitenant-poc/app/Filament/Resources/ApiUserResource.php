<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ApiUserResource\Pages;
use App\Filament\Resources\ApiUserResource\RelationManagers;
use App\Models\ApiUser;
use App\Services\TenantPermissionService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ApiUserResource extends Resource
{
    protected static ?string $model = ApiUser::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationLabel = 'API Users';

    protected static ?string $modelLabel = 'API User';

    protected static ?string $pluralModelLabel = 'API Users';

    /**
     * Check if the resource should be visible in navigation
     */
    public static function shouldRegisterNavigation(): bool
    {
        return TenantPermissionService::canSeeNavigation();
    }

    /**
     * Get the navigation label with tenant context
     */
    public static function getNavigationLabel(): string
    {
        $tenantName = TenantPermissionService::getTenantName();
        return "Users ({$tenantName})";
    }

    /**
     * Check if user can view any records
     */
    public static function canViewAny(): bool
    {
        return TenantPermissionService::canRead();
    }

    /**
     * Check if user can create records
     */
    public static function canCreate(): bool
    {
        return TenantPermissionService::canWrite();
    }

    /**
     * Check if user can edit records
     */
    public static function canEdit($record): bool
    {
        return TenantPermissionService::canWrite();
    }

    /**
     * Check if user can delete records
     */
    public static function canDelete($record): bool
    {
        return TenantPermissionService::canWrite();
    }

    /**
     * Check if user can delete bulk records
     */
    public static function canDeleteAny(): bool
    {
        return TenantPermissionService::canWrite();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('User Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('username')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Select::make('status')
                            ->options([
                                'active' => 'Active',
                                'inactive' => 'Inactive',
                                'pending' => 'Pending',
                            ])
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Contact Information')
                    ->schema([
                        Forms\Components\TextInput::make('phone')
                            ->tel()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('website')
                            ->url()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('company')
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Address Information')
                    ->schema([
                        Forms\Components\TextInput::make('address')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('city')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('zipcode')
                            ->maxLength(10),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Avatar')
                    ->schema([
                        Forms\Components\TextInput::make('avatar')
                            ->url()
                            ->maxLength(255),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('avatar')
                    ->circular()
                    ->defaultImageUrl(function ($record) {
                        return 'https://api.dicebear.com/7.x/avataaars/svg?seed=' . urlencode($record->name);
                    })
                    ->size(40),
                
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->icon('heroicon-m-envelope'),
                
                Tables\Columns\TextColumn::make('username')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('gray'),
                
                Tables\Columns\TextColumn::make('company')
                    ->searchable()
                    ->sortable()
                    ->limit(30)
                    ->tooltip(function ($record) {
                        return $record->company;
                    }),
                
                Tables\Columns\TextColumn::make('city')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'success' => 'active',
                        'danger' => 'inactive',
                        'warning' => 'pending',
                    ])
                    ->searchable(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'pending' => 'Pending',
                    ]),
                
                Tables\Filters\Filter::make('has_website')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('website')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn () => TenantPermissionService::canWrite()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => TenantPermissionService::canWrite()),
                ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->visible(fn () => TenantPermissionService::canWrite())
                    ->label('Create New User'),
            ])
            ->defaultSort('created_at', 'desc')
            ->striped()
            ->paginated([10, 25, 50])
            ->poll('30s');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListApiUsers::route('/'),
            'create' => Pages\CreateApiUser::route('/create'),
            'view' => Pages\ViewApiUser::route('/{record}'),
            'edit' => Pages\EditApiUser::route('/{record}/edit'),
        ];
    }
}
