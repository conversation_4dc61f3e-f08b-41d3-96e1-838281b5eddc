<?php

namespace App\Providers;

use App\BackOffice\CoinPoker\Providers\CoinPokerServiceProvider;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Laravel\Socialite\Facades\Socialite;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->register(CoinPokerServiceProvider::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Relation::morphMap([
            'Tenant' => 'App\BackOffice\Shared\Models\Tenant',
            'User' => 'App\BackOffice\Shared\Models\User',
        ]);

        // Force HTTPS URLs when behind a proxy
        if (request()->header('X-Forwarded-Proto') === 'https') {
            URL::forceScheme('https');
        }

        // Alternative: Force HTTPS in production or when APP_URL is HTTPS
        if (config('app.env') === 'production' || str_starts_with(config('app.url'), 'https://')) {
            URL::forceScheme('https');
        }

        // Configure Google Socialite dynamically based on current domain
        $this->configureGoogleSocialite();
    }

    /**
     * Configure Google Socialite with dynamic redirect URL based on current domain.
     */
    private function configureGoogleSocialite(): void
    {
        if (app()->runningInConsole()) {
            return;
        }

        try {
            $currentDomain = request()->getHost();
            // @todo: make sure the scheme is not hardcoded
            $scheme = 'https';
            $redirectUrl = $scheme.'://'.$currentDomain.'/auth/google/callback';

            Socialite::buildProvider(
                \Laravel\Socialite\Two\GoogleProvider::class,
                [
                    'client_id' => env('GOOGLE_CLIENT_ID'),
                    'client_secret' => env('GOOGLE_CLIENT_SECRET'),
                    'redirect' => $redirectUrl,
                ]
            );

            // Also update the config for consistency
            config(['services.google.redirect' => $redirectUrl]);
        } catch (\Exception $e) {
            // Fallback to env configuration if something goes wrong
            config(['services.google.redirect' => env('GOOGLE_REDIRECT_URI')]);
        }
    }
}
