<?php

namespace App\BackOffice\Shared\Services;

use App\BackOffice\Shared\Models\Tenant;
use Illuminate\Support\Facades\Auth;

class TenantPermissionService
{
    /**
     * Get the current tenant.
     */
    public static function getCurrentTenant(): ?Tenant
    {
        return Tenant::current();
    }

    /**
     * Get the tenant-specific read permission based on current tenant.
     */
    public static function getReadPermission(): ?string
    {
        $tenant = self::getCurrentTenant();

        if (! $tenant) {
            return null;
        }

        // Map tenant domains to their read permissions
        $permissionMap = [
            'coincasino.dev' => 'coincasino_users_section_read',
            'coinpoker.dev' => 'coinpoker_users_section_read',
        ];

        return $permissionMap[$tenant->domain] ?? null;
    }

    /**
     * Get the tenant-specific write permission based on current tenant.
     */
    public static function getWritePermission(): ?string
    {
        $tenant = self::getCurrentTenant();

        if (! $tenant) {
            return null;
        }

        // Map tenant domains to their write permissions
        $permissionMap = [
            'coincasino.dev' => 'coincasino_users_section_write',
            'coinpoker.dev' => 'coinpoker_users_section_write',
        ];

        return $permissionMap[$tenant->domain] ?? null;
    }

    /**
     * Check if the current user can read users for the current tenant.
     */
    public static function canRead(): bool
    {
        $user = Auth::user();

        if (! $user) {
            return false;
        }

        // Superadmin has access to everything
        if ($user->hasRole('superadmin')) {
            return true;
        }

        $permission = self::getReadPermission();

        return $permission ? $user->can($permission) : false;
    }

    public static function canAccess(): bool
    {
        return self::canRead();
    }

    /**
     * Check if the current user can write/create/edit users for the current tenant.
     */
    public static function canWrite(): bool
    {
        $user = Auth::user();

        if (! $user) {
            return false;
        }

        // Superadmin has access to everything
        if ($user->hasRole('superadmin')) {
            return true;
        }

        $permission = self::getWritePermission();

        return $permission ? $user->can($permission) : false;
    }

    /**
     * Check if the current user should see the navigation for the current tenant.
     */
    public static function canSeeNavigation(): bool
    {
        return self::canRead();
    }

    /**
     * Get the tenant name for display purposes.
     */
    public static function getTenantName(): string
    {
        $tenant = self::getCurrentTenant();

        if (! $tenant) {
            return 'Unknown Tenant';
        }

        return $tenant->name;
    }
}
