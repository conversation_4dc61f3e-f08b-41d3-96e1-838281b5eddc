<?php

use Illuminate\Pagination\LengthAwarePaginator;

if (! function_exists('emptyPaginator')) {
    /**
     * Crea un LengthAwarePaginator vacío con valores por defecto.
     */
    function emptyPaginator(): LengthAwarePaginator
    {
        return new LengthAwarePaginator(
            [],
            0,
            50, // perPage hardcodeado
            1   // page hardcodeado
        );
    }
}
