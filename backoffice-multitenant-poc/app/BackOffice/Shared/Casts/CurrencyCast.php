<?php

namespace App\BackOffice\Shared\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class CurrencyCast implements CastsAttributes
{
    public function get(Model $model, string $key, mixed $value, array $attributes): ?string
    {
        if ($value === null) {
            return null;
        }

        $amount = round(($value / 100), 2, PHP_ROUND_HALF_UP);
        return number_format($amount, 2, '.', ' ');
    }

    public function set(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        if ($value === null || $value === '') {
            return null;
        }

        if (is_int($value)) {
            return $value;
        }

        if (is_numeric($value)) {
            return (int) round($value * 100);
        }

        if (is_string($value)) {
            $cleaned = preg_replace('/[^\d\.,\-]/', '', $value);
            
            if (strpos($cleaned, ',') !== false && strpos($cleaned, '.') !== false) {
                $cleaned = str_replace(',', '', $cleaned);
            } elseif (strpos($cleaned, ',') !== false) {
                $parts = explode(',', $cleaned);
                if (count($parts) === 2 && strlen(end($parts)) <= 2) {
                    $cleaned = str_replace(',', '.', $cleaned);
                } else {
                    $cleaned = str_replace(',', '', $cleaned);
                }
            }
            
            if (is_numeric($cleaned)) {
                return (int) round((float) $cleaned * 100);
            }
        }

        return $value;
    }
} 