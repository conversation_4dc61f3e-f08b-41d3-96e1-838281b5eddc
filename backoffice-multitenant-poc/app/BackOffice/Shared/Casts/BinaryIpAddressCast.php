<?php

namespace App\BackOffice\Shared\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class BinaryIpAddressCast implements CastsAttributes
{
    /**
     * Cast the given value.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): ?string
    {
        if (!$value) {
            return null;
        }

        // Convert binary to hex, then to IP
        $hex = bin2hex($value);
        $long = hexdec($hex);
        return long2ip($long);
    }

    /**
     * Prepare the given value for storage.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        if (!$value) {
            return null;
        }

        if (is_string($value) && filter_var($value, FILTER_VALIDATE_IP)) {
            $long = ip2long($value);
            $hex = dechex($long);
            return hex2bin(str_pad($hex, 8, '0', STR_PAD_LEFT));
        }

        return $value;
    }
}
