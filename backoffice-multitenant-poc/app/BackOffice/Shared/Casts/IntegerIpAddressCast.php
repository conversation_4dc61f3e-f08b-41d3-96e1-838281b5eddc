<?php

namespace App\BackOffice\Shared\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class IntegerIpAddressCast implements CastsAttributes
{
    /**
     * Cast the given value from integer to dotted IP notation.
     * Equivalent to <PERSON><PERSON><PERSON>'s helpers\format_ip_num2dot() function.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function get(Model $model, string $key, mixed $value, array $attributes): ?string
    {
        if (!$value || $value === 0) {
            return null;
        }

        return long2ip($value);
    }

    /**
     * Prepare the given value for storage as integer.
     *
     * @param  array<string, mixed>  $attributes
     */
    public function set(Model $model, string $key, mixed $value, array $attributes): mixed
    {
        if (!$value) {
            return null;
        }

        // If it's already an integer, return as is
        if (is_numeric($value)) {
            return (int) $value;
        }

        // If it's a string IP address, convert to integer
        if (is_string($value) && filter_var($value, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return ip2long($value);
        }

        return $value;
    }
} 