<?php

namespace App\BackOffice\Shared\Resources;

use App\BackOffice\Shared\Services\TenantPermissionService;
use Filament\Resources\Resource;

abstract class TenantBaseResource extends Resource
{
    public static function shouldRegisterNavigation(): bool
    {
        return TenantPermissionService::canSeeNavigation();
    }

    public static function canViewAny(): bool
    {
        return TenantPermissionService::canRead();
    }

    public static function canAccess(): bool
    {
        return TenantPermissionService::canAccess();
    }

    public static function canEdit($record): bool
    {
        return TenantPermissionService::canWrite();
    }

    public static function canDelete($record): bool
    {
        return TenantPermissionService::canWrite();
    }

    public static function canDeleteAny(): bool
    {
        return TenantPermissionService::canWrite();
    }
}
