<?php

namespace App\BackOffice\CoinPoker\Pages;

use App\BackOffice\CoinPoker\Services\HandLogService;
use Filament\Pages\Page;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;

class ExportLogs extends Page
{
    protected string $view = 'CoinPoker::poker-game-check.export-logs';

    protected static ?string $title = 'Export Logs';

    protected static ?string $slug = 'export-logs';

    protected static ?string $navigationLabel = 'Export Logs';

    protected static bool $shouldRegisterNavigation = false;

    public string $session = '';

    public string $rawLogs = '';

    public bool $dataLoaded = false;

    public ?string $error = null;

    public function mount(Request $request): void
    {
        $this->session = $request->get('session', '');

        if ($this->session) {
            $this->loadRawLogs();
        }
    }

    protected function loadRawLogs(): void
    {
        try {
            $handLogService = app(HandLogService::class);
            $logs = $handLogService->find_by_table_session($this->session);

            $rawOutput = '';
            foreach ($logs as $log) {
                $rawOutput .= $log->text."\n";
            }

            $this->rawLogs = $rawOutput;
            $this->dataLoaded = true;

        } catch (\Exception $e) {
            $this->error = 'Error loading hand data: '.$e->getMessage();
        }
    }

    /**
     * Get the page title with session info
     */
    public function getTitle(): string
    {
        return $this->session ? "Export Logs - Session: {$this->session}" : static::$title;
    }

    public function render(): View
    {
        return view('CoinPoker::poker-game-check.export-logs', ['rawLogs' => $this->rawLogs])
            ->layout('Shared::layouts.raw');
    }
}
