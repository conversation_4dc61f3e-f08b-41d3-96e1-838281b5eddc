<?php

namespace App\BackOffice\CoinPoker\Pages;

use Filament\Pages\Page;
use Illuminate\Http\Request;

class HandReplayer extends Page
{
    protected string $view = 'CoinPoker::poker-game-check.hand-replayer';

    protected static ?string $title = 'Hand Replayer';

    protected static ?string $slug = 'hand-replayer';

    protected static ?string $navigationLabel = 'Hand Replayer';

    protected static bool $shouldRegisterNavigation = false;

    public string $session = '';

    public ?string $error = null;

    public function mount(Request $request): void
    {
        $this->session = $request->get('session', '');
    }

    public function getExportUrl(): string
    {
        return ExportLogs::getUrl(['session' => $this->session]);
    }
}
