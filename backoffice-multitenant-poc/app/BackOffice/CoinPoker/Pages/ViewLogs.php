<?php

namespace App\BackOffice\CoinPoker\Pages;

use App\BackOffice\CoinPoker\Services\CardFormattingService;
use App\BackOffice\CoinPoker\Services\HandLogService;
use Filament\Pages\Page;
use Illuminate\Http\Request;

class ViewLogs extends Page
{
    protected string $view = 'CoinPoker::poker-game-check.view-logs';

    protected static ?string $title = 'View Logs';

    protected static ?string $slug = 'view-logs';

    protected static ?string $navigationLabel = 'View Logs';

    protected static bool $shouldRegisterNavigation = false; // Don't show in navigation since it's accessed via direct links

    public string $session = '';

    public string $formattedLogs = '';

    public bool $dataLoaded = false;

    public ?string $error = null;

    public function mount(Request $request): void
    {
        $this->session = $request->get('session', '');

        if ($this->session) {
            $this->loadAndFormatHandData();
        }
    }

    protected function loadAndFormatHandData(): void
    {
        try {
            $handLogService = app(HandLogService::class);
            $cardFormattingService = app(CardFormattingService::class);

            // Get hand logs from MongoDB
            $hands = $handLogService->find_by_table_session($this->session);

            // Convert to array and format with card display
            $logsArray = [];
            foreach ($hands as $hand) {
                $logsArray[] = json_decode(json_encode($hand), true);
            }

            // Format all logs with card rendering
            $this->formattedLogs = $cardFormattingService->formatLogs($logsArray);
            $this->dataLoaded = true;

        } catch (\Exception $e) {
            $this->error = 'Error loading hand data: '.$e->getMessage();
        }
    }

    /**
     * Get the page title with session info
     */
    public function getTitle(): string
    {
        return $this->session ? "session: {$this->session}" : static::$title;
    }
}
