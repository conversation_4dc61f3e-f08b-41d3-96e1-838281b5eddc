<?php

namespace App\BackOffice\CoinPoker\Pages;

use App\BackOffice\CoinPoker\Services\HandLogService;
use Filament\Actions\Action;
use Filament\Pages\Page;

class HandDetails extends Page
{
    protected static ?string $title = 'Hand Details';

    protected static ?string $slug = 'hand-details';

    protected string $view = 'CoinPoker::pages.hand-details';

    public ?string $handId = null;

    public ?array $hand = null;

    public ?string $error = null;

    public function mount($handId = null): void
    {
        $this->handId = $handId ?: request()->get('handId');

        if ($this->handId) {
            $this->loadHandDetails($this->handId);
        }
    }

    public function loadHandDetails(string $handId): void
    {
        try {
            $handLogService = new HandLogService;

            $hand = $handLogService->findHandById((int) $handId);

            if (! $hand) {
                $this->error = 'Hand not found';

                return;
            }

            $this->hand = $hand;

        } catch (\Exception $e) {
            $this->error = 'Error retrieving hand details: '.$e->getMessage();
        }
    }

    public function searchHand(): void
    {
        $this->validate([
            'handId' => 'required|string|max:255',
        ]);

        if (! empty($this->handId)) {
            $this->loadHandDetails($this->handId);
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back to Players')
                ->url(PlayerResource::getUrl())
                ->color('gray'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }
}
