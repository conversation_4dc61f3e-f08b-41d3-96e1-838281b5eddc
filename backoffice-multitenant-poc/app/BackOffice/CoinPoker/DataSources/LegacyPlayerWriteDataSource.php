<?php

namespace App\BackOffice\CoinPoker\DataSources;

use App\BackOffice\CoinPoker\Contracts\PlayerWriteDataSourceInterface;
use App\BackOffice\CoinPoker\Models\GamingSystem\OfcSeatCaptcha;
use App\BackOffice\CoinPoker\Models\GamingSystem\PlayerAccount;
use App\BackOffice\CoinPoker\Models\GamingSystem\RestrictionGroup;
use Illuminate\Support\Facades\DB;

class LegacyPlayerWriteDataSource implements PlayerWriteDataSourceInterface
{
    public function updateRestrictionGroup($groupId, array $data): bool
    {
        $group = RestrictionGroup::find($groupId);
        if (! $group) {
            return false;
        }
        if (isset($data['name'])) {
            $group->name = $data['name'];
        }
        if (isset($data['players'])) {
            $group->players()->sync($data['players']);
        }
        $group->save();

        return true;
    }

    public function createRestrictionGroup(array $data): bool
    {
        $group = new RestrictionGroup;
        if (isset($data['name'])) {
            $group->name = $data['name'];
        }
        $group->save();
        if (isset($data['players']) && is_array($data['players'])) {
            $group->players()->sync($data['players']);
        }

        return true;
    }

    public function deleteRestrictionGroups(array $groupIds): bool
    {
        $groups = RestrictionGroup::whereIn('id', $groupIds)->get();
        foreach ($groups as $group) {
            $group->players()->detach();
            $group->delete();
        }

        return true;
    }

    public function addCaptcha(int $playerId): bool
    {
        OfcSeatCaptcha::updateOrCreate(
            [
                'player_id' => $playerId,
            ],
            [
                'pending' => 1,
                'verified' => 0,
                'entry_fail' => 0,
                'date_created' => now(),
            ]
        );

        return true;
    }

    public function banPlayersByIp(int $ip): array
    {
        $players = DB::connection('coinpoker.gaming_system')
            ->select('
                SELECT a.player_id, a.nick_name, a.email, tmp.cnt, tmp.ip, a.banned
                FROM player_session s
                JOIN player_account a ON s.player_id = a.player_id
                JOIN (
                    SELECT ip, COUNT(DISTINCT player_id) as cnt
                    FROM player_session
                    WHERE ip = ?
                    GROUP BY ip
                    HAVING cnt >= 4
                ) tmp ON s.ip = tmp.ip
                GROUP BY a.player_id, tmp.ip
                ORDER BY banned ASC
            ', [$ip]);

        $bannedPlayers = [];

        foreach ($players as $player) {
            // Update the banned status to 1 (true)
            PlayerAccount::where('player_id', $player->player_id)
                ->update(['banned' => 1]);

            $bannedPlayers[] = [
                'player_id' => $player->player_id,
                'nick_name' => $player->nick_name,
                'email' => $player->email,
                'was_already_banned' => (bool) $player->banned,
            ];
        }

        return $bannedPlayers;
    }

    public function updatePlayerBanStatus(int $playerId, bool $shouldBan): bool
    {
        $updated = PlayerAccount::where('player_id', $playerId)
            ->update(['banned' => $shouldBan]);

        return $updated > 0;
    }

    public function setConstantScreenshoting(int $playerId, bool $enable): bool
    {
        // Replicate the Drupal logic from players_tracking_constant_screenshoting function
        $state = $enable ? 1 : 0;

        // Valid states are 0 and 1
        $validStates = [0, 1];
        if (! in_array($state, $validStates)) {
            return false;
        }

        $updated = PlayerAccount::where('player_id', $playerId)
            ->update(['constant_screenshoting' => $state]);

        return $updated > 0;
    }
}
