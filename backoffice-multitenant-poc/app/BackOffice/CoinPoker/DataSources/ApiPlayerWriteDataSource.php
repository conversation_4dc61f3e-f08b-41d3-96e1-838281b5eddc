<?php

namespace App\BackOffice\CoinPoker\DataSources;

use App\BackOffice\CoinPoker\Contracts\PlayerWriteDataSourceInterface;

class ApiPlayerWriteDataSource implements PlayerWriteDataSourceInterface
{
    public function updateRestrictionGroup($groupId, array $data): bool
    {
        // (Replace with real API call)
        return true;
    }

    public function createRestrictionGroup(array $data): bool
    {
        // (Replace with real API call)
        return true;
    }

    public function deleteRestrictionGroups(array $groupIds): bool
    {
        // (Replace with real API call)
        return true;
    }

    public function addCaptcha(int $playerId): bool
    {
        // Replace with real API call
        return true;
    }

    public function banPlayersByIp(int $ip): array
    {
        // This would call an API endpoint to ban all players using a specific IP
        return [];
    }

    public function updatePlayerBanStatus(int $playerId, bool $shouldBan): bool
    {
        // This would call an API endpoint like PUT /api/players/{playerId}/ban-status
        return true;
    }

    public function setConstantScreenshoting(int $playerId, bool $enable): bool
    {
        // This would call an API endpoint like PUT /api/players/{playerId}/constant-screenshoting
        // Body: {"enabled": true/false}
        return true;
    }
}
