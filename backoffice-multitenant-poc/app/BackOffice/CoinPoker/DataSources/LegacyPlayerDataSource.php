<?php

namespace App\BackOffice\CoinPoker\DataSources;

use App\BackOffice\CoinPoker\Contracts\PlayerDataSourceInterface;
use App\BackOffice\CoinPoker\Models\GamingSystem\PlayerAccount;
use App\BackOffice\CoinPoker\Models\GamingSystem\PlayerSession;
use App\BackOffice\CoinPoker\Models\GamingSystem\RestrictionGroup;
use App\BackOffice\CoinPoker\Services\AccountingApiService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LegacyPlayerDataSource implements PlayerDataSourceInterface
{
    /**
     * @return LengthAwarePaginator|Collection
     */
    public function getPlayers(array $filters, array $resultSetOptions, array $sorting = [], array $extra = [])
    {
        $page = $resultSetOptions['page'] ?? null;
        $perPage = $resultSetOptions['perPage'] ?? null;
        $skip = $resultSetOptions['skip'] ?? null;
        $limit = $resultSetOptions['limit'] ?? null;
        $take = $resultSetOptions['take'] ?? null;

        $withRelations = $extra['with'] ?? [];
        $withTags = in_array('tags', $withRelations);
        $withRestrictedGroups = in_array('restrictedGroups', $withRelations);

        $query = PlayerAccount::query();

        // Load relationships based on 'with' array
        if ($withRestrictedGroups) {
            $query->with('restrictionGroups');
        }

        // Handle tags separately since it uses a join
        if ($withTags) {
            $query->from('player_account as pa')
                ->leftJoin('player_tag_cache as ptc', 'pa.player_id', '=', 'ptc.player_id')
                ->select(['pa.*', 'ptc.tags']);
        }

        $tablePrefix = $withTags ? 'pa.' : '';

        $query
            ->when($filters['search'] ?? null,
                fn ($q, $search) => $q->where(fn ($subQ) => $subQ->where($tablePrefix.'player_id', 'like', '%'.$search.'%')
                    ->orWhere($tablePrefix.'nick_name', 'like', '%'.$search.'%')
                    ->orWhere($tablePrefix.'email', 'like', '%'.$search.'%')
                )
            )
            ->when($filters['player_id'] ?? null, fn ($q, $playerId) => $q->where($tablePrefix.'player_id', $playerId))
            ->when($filters['player_ids'] ?? null, fn ($q, $playerIds) => $q->whereIn($tablePrefix.'player_id', $playerIds))
            ->when($filters['player_provider_ids'] ?? null, fn ($q, array $playerProviderIds) => $q->whereIn($tablePrefix.'player_provider_id', $playerProviderIds))
            ->when($filters['provider_name'] ?? null, fn ($q, $providerName) => $q->whereHas('provider', fn ($subQ) => $subQ->where('provider_name', $providerName)))
            ->when($filters['nick_name'] ?? null, fn ($q, $nickName) => $q->where($tablePrefix.'nick_name', 'like', '%'.$nickName.'%'))
            ->when($filters['email'] ?? null, fn ($q, $email) => $q->where($tablePrefix.'email', 'like', '%'.$email.'%'))
            ->when(isset($filters['banned']) ? $filters['banned'] : null, fn ($q, $banned) => $q->where($tablePrefix.'banned', (bool) $banned))
            ->when($filters['tags'] ?? null, function ($q, $tags) {
                $tagIds = array_values($tags);
                $q->where(function ($subQ) use ($tagIds) {
                    foreach ($tagIds as $tagId) {
                        if ($tagId == 0) {
                            $subQ->orWhereNull('ptc.tags');
                        } else {
                            $subQ->orWhere('ptc.tags', 'like', "%'$tagId'%");
                        }
                    }
                });
            });

        $query->when(! empty($sorting) && ! empty($sorting['column']), function ($q) use ($sorting, $tablePrefix) {
            $sortColumn = $sorting['column'];
            $sortDirection = $sorting['direction'] ?? 'asc';

            $columnMapping = [
                'player_id' => $tablePrefix.'player_id',
                'nick_name' => $tablePrefix.'nick_name',
                'email' => $tablePrefix.'email',
                'balance_money' => $tablePrefix.'balance_money',
                'cur_proxy_id' => $tablePrefix.'cur_proxy_id',
                'cur_ip' => $tablePrefix.'cur_ip',
                'last_login' => $tablePrefix.'last_login',
                'login_count' => $tablePrefix.'login_count',
                'banned' => $tablePrefix.'banned',
                'tags' => 'ptc.tags',
            ];

            $dbColumn = $columnMapping[$sortColumn] ?? $tablePrefix.'last_login';
            $q->orderBy($dbColumn, $sortDirection);
        }, fn ($q) => $q->orderBy($tablePrefix.'last_login', 'desc'));
        if ($page !== null && $perPage !== null) {
            return $query->paginate(perPage: $perPage, page: $page)
                ->through(fn ($item) => $this->transformPlayerData($item));
        } else {
            return $query
                ->when($skip, fn ($q) => $q->skip($skip))
                ->when($limit, fn ($q) => $q->limit($limit))
                ->when($take, fn ($q) => $q->take($take))
                ->get()
                ->map(fn ($item) => $this->transformPlayerData($item));
        }
    }

    /**
     * Transform player data to match expected format
     */
    private function transformPlayerData($player): array
    {
        $data = [
            'player_id' => $player->player_id,
            'nick_name' => $player->nick_name,
            'email' => $player->email,
            'balance_money' => $player->balance_money,
            // Transform status: cur_proxy_id 0 = Offline, >0 = Online (matches Drupal logic)
            'status' => $player->cur_proxy_id == 0 ? 'Offline' : 'Online',
            'cur_proxy_id' => $player->cur_proxy_id, // Keep original for sorting
            'cur_ip' => $player->cur_ip,
            'last_login' => $player->last_login,
            'login_count' => $player->login_count,
            'banned' => $player->banned,
        ];

        // Add tags if available (from join)
        if (property_exists($player, 'tags')) {
            $data['tags'] = $player->tags;
        }

        // Add restricted groups if the relationship is loaded
        if ($player->relationLoaded('restrictionGroups')) {
            $data['restricted_groups'] = $player->restrictionGroups->map(function ($group) {
                return [
                    'id' => $group->id,
                    'name' => $group->name,
                ];
            })->toArray();
        } else {
            $data['restricted_groups'] = [];
        }

        return $data;
    }

    /**
     * Get players for provider table using Accounting API
     */
    public function getPlayersForProviderTable(array $filters, int $page, int $perPage, array $sorting = [], ?string $search = null): LengthAwarePaginator
    {
        try {
            $accountingApi = app(AccountingApiService::class);
            $args = [];

            if (! empty($filters['player_provider_id'])) {
                $args['id'] = $filters['player_provider_id'];
            }
            if (! empty($filters['nick_name'])) {
                $args['nick_name'] = $filters['nick_name'];
            }
            if (! empty($filters['email'])) {
                $args['email'] = $filters['email'];
            }
            if (! empty($filters['phone_number'])) {
                $args['phone_number'] = $filters['phone_number'];
            }

            if (empty($args)) {
                return emptyPaginator();
            }

            $result = $accountingApi->findPlayers($args);

            if (! $result) {
                return emptyPaginator();
            }

            $transformedData = collect($result)->map(function ($row) {
                // @todo: move this to the proper place
                return [
                    'provider_player_id' => $row['id'] ?? 'N/A',
                    'email' => $row['email'] ?? 'N/A',
                    'nickname' => $row['nickname'] ?? 'N/A',
                    'phone_number' => $row['phone_number'] ?? 'N/A',
                    'phone_confirmation_info' => $row['phone_confirmation_info'] ?? 'N/A',
                ];
            })->toArray();

            return new LengthAwarePaginator(
                array_slice($transformedData, ($page - 1) * $perPage, $perPage),
                count($transformedData),
                $perPage,
                $page,
                [
                    'path' => request()->url(),
                    'pageName' => 'page',
                ]
            );

        } catch (\Exception $e) {
            report($e);

            return emptyPaginator();
        }
    }

    /**
     * Replicate Drupal: $player_info = \o\get('accounting_api')->getPlayerInfo($player->player_provider_id);
     */
    public function getAccountingApiPlayerInfo(string $playerId): ?array
    {
        $player = PlayerAccount::where('player_id', $playerId)->first();
        $accountingApi = app(AccountingApiService::class);
        try {
            return $accountingApi->getPlayerInfo($player->player_provider_id);
        } catch (\Exception $e) {
            report($e);

            return null;
        }
    }

    public function getPokerGameSessions(array $filters, int $page, int $perPage, array $sorting = [], ?string $search = null)
    {
        try {
            // Always use the poker_logs database approach for consistency with Drupal
            // This handles all the cross-database relationships properly
            return $this->getPokerGameSessionsFromLogs($filters, $page, $perPage, $sorting, $search);
        } catch (\Exception $e) {
            Log::error('Legacy Player DataSource - getPokerGameSessions error: '.$e->getMessage());
            throw $e;
        }
    }

    /**
     * Get poker game sessions using raw SQL for cross-database join
     * This replicates the Drupal cross-database join logic
     */
    private function getPokerGameSessionsFromLogs(array $filters, int $page, int $perPage, array $sorting = [], ?string $search = null)
    {
        // Use raw SQL for cross-database join since Eloquent doesn't handle this well
        $query = DB::connection('coinpoker.poker_logs')
            ->table('player_session as ps')
            ->join('gaming_system.player_account as pa', 'ps.player_id', '=', 'pa.player_id')
            ->select([
                'ps.player_session_id',
                'ps.table_id',
                'ps.player_id',
                'ps.game_ssid',
                'ps.from_date',
                'ps.game_desc',
                'pa.nick_name as player_name',
            ])->when($filters['from'] ?? null, fn ($q, $fromDate) => $q->where('ps.from_date', '>=', $fromDate))
            ->when($filters['to'] ?? null, fn ($q, $toDate) => $q->where('ps.from_date', '<=', $toDate))
            ->when($filters['session_id'] ?? null, fn ($q, $sessionId) => $q->where('ps.game_ssid', $sessionId))
            ->when($filters['table_id'] ?? null, fn ($q, $tableId) => $q->where('ps.table_id', $tableId))
            ->when($filters['nick_name'] ?? null, fn ($q, $nickName) => $q->where('pa.nick_name', 'like', '%'.$nickName.'%'));

        if (! empty($sorting) && ! empty($sorting['column'])) {
            $direction = $sorting['direction'] ?? 'asc';
            $query->orderBy($sorting['column'], $direction);
        } else {
            $query->orderBy('ps.from_date', 'desc');
        }

        return $query->paginate(perPage: $perPage, page: $page)
            ->through(fn ($item) => [
                'table_id' => $item->table_id,
                'player_name' => $item->player_name ?? 'Unknown',
                'from_date' => $item->from_date,
                'game_ssid' => $item->game_ssid,
                'player_id' => $item->player_id,
                'game_desc' => $item->game_desc,
            ]);
    }

    public function getRestrictionGroups(array $filters, int $page, int $perPage, array $sorting = []): \Illuminate\Pagination\LengthAwarePaginator
    {
        $query = RestrictionGroup::query()->with('players');

        if (! empty($filters['name'])) {
            $query->where('name', 'like', '%'.$filters['name'].'%');
        }

        if (! empty($filters['players'])) {
            $query->whereHas('players', function ($q) use ($filters) {
                $q->whereIn($q->qualifyColumn('player_id'), (array) $filters['players']);
            });
        }

        if (! empty($sorting) && ! empty($sorting['column'])) {
            $direction = $sorting['direction'] ?? 'asc';
            $column = $sorting['column'] === 'name' ? 'name' : 'id';
            $query->orderBy($column, $direction);
        } else {
            $query->orderBy('id', 'desc');
        }

        return $query->paginate($perPage, ['*'], 'page', $page)
            ->through(function ($restrictionGroup) {
                return [
                    'id' => $restrictionGroup->id,
                    'name' => $restrictionGroup->name,
                    'players' => $restrictionGroup->players->pluck('nick_name', 'player_id')->toArray(),
                ];
            });
    }

    /**
     * Get a single restriction group by ID
     */
    public function getRestrictionGroupById(int|string $id): ?array
    {
        $group = RestrictionGroup::with('players')->find($id);
        if (! $group) {
            return null;
        }

        return [
            'id' => $group->id,
            'name' => $group->name,
            'players' => $group->players->pluck('nick_name', 'player_id')->toArray(),
        ];
    }

    /**
     * Get players with constant screenshoting enabled for tracking
     * Based on the query from drupal/sites/all/modules/ofcp_players/src/actions/player_tracking.php:25
     */
    public function getPlayersTracking(array $filters, array $resultSetOptions, array $sorting = [])
    {
        $page = $resultSetOptions['page'] ?? null;
        $perPage = $resultSetOptions['perPage'] ?? null;

        $query = PlayerAccount::from('player_account as pa')
            ->select([
                'pa.player_id',
                'pa.nick_name',
                'pa.cur_proxy_id',
                'pa.cur_ip',
                'pa.last_login',
                'pa.login_count',
                'pa.crash_count',
                'pa.banned',
            ])
            ->where('pa.constant_screenshoting', 1);

        // Apply filters
        $query->when($filters['search'] ?? null, fn ($q, $search) => $q->where(fn ($subQ) => $subQ->where('pa.player_id', 'like', '%'.$search.'%')
            ->orWhere('pa.nick_name', 'like', '%'.$search.'%')
        ))
            ->when($filters['player_id'] ?? null, fn ($q, $playerId) => $q->where('pa.player_id', $playerId))
            ->when($filters['nick_name'] ?? null, fn ($q, $nickName) => $q->where('pa.nick_name', 'like', '%'.$nickName.'%'));

        // Apply sorting
        $query->when(! empty($sorting) && ! empty($sorting['column']), function ($q) use ($sorting) {
            $sortColumn = $sorting['column'];
            $sortDirection = $sorting['direction'] ?? 'desc';

            $columnMapping = [
                'player_id' => 'pa.player_id',
                'nick_name' => 'pa.nick_name',
                'cur_proxy_id' => 'pa.cur_proxy_id',
                'ip_address' => 'pa.cur_ip',
                'last_login' => 'pa.last_login',
                'login_count' => 'pa.login_count',
                'crash_count' => 'pa.crash_count',
                'banned' => 'pa.banned',
            ];

            $dbColumn = $columnMapping[$sortColumn] ?? 'pa.last_login';
            $q->orderBy($dbColumn, $sortDirection);
        }, fn ($q) => $q->orderBy('pa.last_login', 'desc'));

        if ($page !== null && $perPage !== null) {
            return $query->paginate(perPage: $perPage, page: $page)
                ->through(fn ($item) => $this->transformPlayersTrackingData($item));
        } else {
            return $query->get()
                ->map(fn ($item) => $this->transformPlayersTrackingData($item));
        }
    }

    private function transformPlayersTrackingData(PlayerAccount $player): array
    {
        return [
            'player_id' => $player->player_id,
            'nick_name' => $player->nick_name,
            'cur_proxy_id' => $player->cur_proxy_id,
            'ip_address' => $this->formatIpAddress($player->cur_ip),
            'last_login' => $player->last_login,
            'login_count' => $player->login_count,
            'crash_count' => $player->crash_count,
            'banned' => $player->banned,
        ];
    }

    /**
     * Format IP address from numeric to dotted notation
     * Based on helpers\format_ip_num2dot function from Drupal
     */
    private function formatIpAddress($ipNumber): string
    {
        if (! $ipNumber) {
            return '';
        }

        // Convert numeric IP to dotted notation
        return long2ip($ipNumber);
    }

    /**
     * Get players captcha tracking data
     * Based on the query from drupal/sites/all/modules/ofcp_players/src/actions/player_tracking.php:204
     */
    public function getPlayersCaptchaTracking(array $filters, array $resultSetOptions, array $sorting = [])
    {
        $page = $resultSetOptions['page'] ?? null;
        $perPage = $resultSetOptions['perPage'] ?? null;

        $query = DB::connection('coinpoker.gaming_system')
            ->table('ofc_seat_captcha as osc')
            ->join('player_account as pa', 'osc.player_id', '=', 'pa.player_id')
            ->select([
                'osc.player_id',
                'pa.nick_name',
                'pa.cur_proxy_id',
                'osc.entry_fail',
                'osc.verified',
                'osc.pending',
                'osc.date_created',
                'osc.date_verified',
            ]);

        // Apply filters
        $query->when($filters['search'] ?? null, fn ($q, $search) => $q->where(fn ($subQ) => $subQ->where('osc.player_id', 'like', '%'.$search.'%')
            ->orWhere('pa.nick_name', 'like', '%'.$search.'%')
        ))
            ->when($filters['player_id'] ?? null, fn ($q, $playerId) => $q->where('osc.player_id', $playerId))
            ->when($filters['nick_name'] ?? null, fn ($q, $nickName) => $q->where('pa.nick_name', 'like', '%'.$nickName.'%'));

        // Apply sorting
        $query->when(! empty($sorting) && ! empty($sorting['column']), function ($q) use ($sorting) {
            $sortColumn = $sorting['column'];
            $sortDirection = $sorting['direction'] ?? 'desc';

            $columnMapping = [
                'player_id' => 'osc.player_id',
                'nick_name' => 'pa.nick_name',
                'cur_proxy_id' => 'pa.cur_proxy_id',
                'entry_fail' => 'osc.entry_fail',
                'verified' => 'osc.verified',
                'pending' => 'osc.pending',
                'date_created' => 'osc.date_created',
                'date_verified' => 'osc.date_verified',
            ];

            $dbColumn = $columnMapping[$sortColumn] ?? 'osc.date_created';
            $q->orderBy($dbColumn, $sortDirection);
        }, fn ($q) => $q->orderBy('osc.date_created', 'desc'));

        if ($page !== null && $perPage !== null) {
            return $query->paginate(perPage: $perPage, page: $page)
                ->through(fn ($item) => $this->transformPlayersCaptchaTrackingData($item));
        } else {
            return $query->get()
                ->map(fn ($item) => $this->transformPlayersCaptchaTrackingData($item));
        }
    }

    /**
     * Transform players captcha tracking data to match expected format
     */
    private function transformPlayersCaptchaTrackingData($captcha): array
    {
        return [
            'player_id' => $captcha->player_id,
            'nick_name' => $captcha->nick_name,
            'cur_proxy_id' => $captcha->cur_proxy_id,
            'entry_fail' => $captcha->entry_fail,
            'verified' => (bool) $captcha->verified,
            'pending' => (bool) $captcha->pending,
            'date_created' => $captcha->date_created,
            'date_verified' => $captcha->date_verified,
        ];
    }

    /**
     * Get players heatmap data for anti-cheat analysis
     * Based on the query from drupal/sites/all/modules/ofcp_players/src/actions/player_tracking.php:334
     */
    public function getPlayersHeatmap(array $filters, array $resultSetOptions, array $sorting = [])
    {
        $page = $resultSetOptions['page'] ?? null;
        $perPage = $resultSetOptions['perPage'] ?? null;

        $query = DB::connection('coinpoker.gaming_system')
            ->table('anti_cheat_heatmap as ach')
            ->join('player_account as pa', 'ach.player_id', '=', 'pa.player_id')
            ->select([
                'ach.player_id',
                'pa.nick_name',
                DB::raw('MIN(ach.eval_result) as min_eval_result'),
                DB::raw('(SELECT anti_cheat_heatmap_id FROM anti_cheat_heatmap WHERE player_id = ach.player_id AND eval_result = MIN(ach.eval_result) LIMIT 1) as heatmap_id'),
            ])
            ->whereRaw('ROUND((LENGTH(ach.points) - 1) / 6 / ach.eval_result) >= ?', [$filters['click_count'] ?? 30])
            ->where('ach.eval_result', '>=', $filters['from_eval'] ?? 0)
            ->where('ach.eval_result', '<=', $filters['to_eval'] ?? 0.5)
            ->groupBy(['ach.player_id', 'pa.nick_name']);

        // Apply filters
        $query->when($filters['player_id'] ?? null, fn ($q, $playerId) => $q->where('ach.player_id', $playerId))
            ->when($filters['nick_name'] ?? null, fn ($q, $nickName) => $q->where('pa.nick_name', 'like', '%'.$nickName.'%'));

        // Apply sorting
        $query->when(! empty($sorting) && ! empty($sorting['column']), function ($q) use ($sorting) {
            $sortColumn = $sorting['column'];
            $sortDirection = $sorting['direction'] ?? 'asc';

            $columnMapping = [
                'player_id' => 'ach.player_id',
                'nick_name' => 'pa.nick_name',
                'min_eval_result' => 'min_eval_result',
            ];

            $dbColumn = $columnMapping[$sortColumn] ?? 'ach.player_id';
            $q->orderBy($dbColumn, $sortDirection);
        }, fn ($q) => $q->orderBy('ach.player_id', 'asc'));

        // Limit to 50 as per Drupal implementation
        $query->limit(50);

        if ($page !== null && $perPage !== null) {
            return $query->paginate(perPage: min($perPage, 50), page: $page)
                ->through(fn ($item) => $this->transformPlayersHeatmapData($item));
        } else {
            return $query->get()
                ->map(fn ($item) => $this->transformPlayersHeatmapData($item));
        }
    }

    /**
     * Transform players heatmap data to match expected format
     */
    private function transformPlayersHeatmapData($heatmap): array
    {
        return [
            'player_id' => $heatmap->player_id,
            'nick_name' => $heatmap->nick_name,
            'min_eval_result' => $heatmap->min_eval_result,
            'heatmap_id' => $heatmap->heatmap_id,
            // Format evaluation result with red color for suspicious values < 0.6
            'evaluation' => $heatmap->min_eval_result < 0.6 ?
                ['value' => $heatmap->min_eval_result, 'suspicious' => true] :
                ['value' => $heatmap->min_eval_result, 'suspicious' => false],
        ];
    }

    /**
     * Get multi-account check data (devices or IP-based tracking)
     * Replicated logic from Drupal player_multi_account_check.php
     */
    public function getMultiAccountData(array $filters, int $page, int $recordsPerPage): LengthAwarePaginator
    {
        $trackingType = $filters['count_by_devices'] ?? 'devices';
        $countByDevices = $trackingType === 'devices';
        $fromDate = $filters['from_date'] ?? now()->subDays(10)->format('Y-m-d H:i:s');
        $accCnt = (int) $filters['acc_cnt'] ?? 4;
        $ip = $filters['ip'] ?? null;
        $playerId = (int) $filters['player_id'] ?? null;

        if ($countByDevices) {
            return $this->getMultiAccountDeviceData($fromDate, $ip, $accCnt, $page, $recordsPerPage);
        } else {
            return $this->getMultiAccountIpData($fromDate, $ip, $playerId, $accCnt, $page, $recordsPerPage);
        }
    }

    /**
     * Get device-based multi-account data using Accounting API
     */
    private function getMultiAccountDeviceData(string $fromDate, ?string $ip, int $accCnt, int $page, int $recordsPerPage): LengthAwarePaginator
    {
        try {
            $accountingApiService = app(AccountingApiService::class);

            $deviceHashes = null;
            if ($ip) {
                $deviceHashes = array_map('trim', explode(',', $ip));
            }

            $data = $accountingApiService->getDevicesReport($fromDate, $deviceHashes, $accCnt);

            $items = collect($data)->map(function ($item) {
                return [
                    'id' => $item['id'] ?? null, // Device ID needed for quota change
                    'hash' => $item['hash'] ?? 'N/A',
                    'confirmed_count' => $item['confirmed_count'] ?? 0,
                    'quota' => $item['quota'] ?? 0,
                    'banned' => $item['banned'] ? 'Yes' : 'No',
                    'ip' => null,
                    'cnt' => null,
                    'player_id' => null,
                    'login_date' => null,
                    'game_ssid' => null,
                ];
            });

            // Manual pagination for API data
            $total = $items->count();
            $offset = ($page - 1) * $recordsPerPage;
            $paginatedItems = $items->slice($offset, $recordsPerPage);

            return new LengthAwarePaginator(
                $paginatedItems->values(),
                $total,
                $recordsPerPage,
                $page,
                ['path' => request()->url(), 'pageName' => 'page']
            );

        } catch (\Exception $e) {
            Log::error('LegacyPlayerDataSource - getMultiAccountDeviceData error: '.$e->getMessage());

            return emptyPaginator();
        }
    }

    /**
     * Get IP-based multi-account data using Eloquent models (refactored from raw SQL)
     */
    private function getMultiAccountIpData(string $fromDate, ?string $ip, ?int $playerId, int $accCnt, int $page, int $recordsPerPage): LengthAwarePaginator
    {
        $query = PlayerSession::selectRaw('ip, COUNT(DISTINCT player_id) as cnt, GROUP_CONCAT(DISTINCT player_id) as player_ids_concat')
            ->where('login_date', '>=', $fromDate)
            ->groupBy('ip')
            ->havingRaw('cnt >= ?', [$accCnt])
            ->orderByDesc('cnt');

        // Player ID filtering - get IPs used by specific player
        if (! empty($playerId)) {
            $playerIps = PlayerSession::where('player_id', $playerId)
                ->where('login_date', '>=', $fromDate)
                ->distinct()
                ->pluck('ip')
                ->toArray();

            if ($playerIps) {
                $query->whereIn('ip', $playerIps);
            } else {
                // No IPs found for this player - return empty result
                $query->whereRaw('1 = 0');
            }
        }

        // IP filtering - convert dotted notation to numeric if needed
        if (! empty($ip)) {
            $ipNumeric = is_numeric($ip) ? $ip : ip2long($ip);
            if ($ipNumeric !== false) {
                $query->where('ip', $ipNumeric);
            }
        }

        // Get results and transform
        $results = $query->paginate($recordsPerPage, ['*'], 'page', $page);

        return $results->through(function ($item) {
            // Convert comma-separated player IDs to array of integers
            $playerIds = $item->player_ids_concat
                ? array_map('intval', explode(',', $item->player_ids_concat))
                : [];

            return [
                'hash' => null,
                'confirmed_count' => null,
                'quota' => null,
                'banned' => null,
                'ip' => $item->ip,
                'cnt' => $item->cnt,
                'player_id' => null,
                'login_date' => null,
                'game_ssid' => null,
                'player_ids_with_same_ip' => $playerIds,
            ];
        });
    }

    /**
     * Get a single player by player ID
     */
    public function getPlayer(int|array $criteria): ?array
    {
        $query = PlayerAccount::query();

        if (is_int($criteria)) {
            // Backward compatibility: integer means player_id
            $query->where('player_id', $criteria);
        } else {
            // Handle criteria array
            foreach ($criteria as $field => $value) {
                switch ($field) {
                    case 'id':
                    case 'player_id':
                        $query->where('player_id', $value);
                        break;
                    case 'nickname':
                    case 'nick_name':
                        $query->where('nick_name', $value);
                        break;
                    case 'email':
                        $query->where('email', $value);
                        break;
                    default:
                        // For any other field, use it directly
                        $query->where($field, $value);
                        break;
                }
            }
        }

        $player = $query->first();

        if (! $player) {
            return null;
        }

        return [
            'player_id' => $player->player_id,
            'nick_name' => $player->nick_name,
            'email' => $player->email,
            'balance_total' => $player->balance_total,
            'banned' => $player->banned,
            'balance_money' => $player->balance_money,
            'cur_proxy_id' => $player->cur_proxy_id,
            'cur_ip' => $player->cur_ip,
            'last_login' => $player->last_login,
            'login_count' => $player->login_count,
        ];
    }
}
