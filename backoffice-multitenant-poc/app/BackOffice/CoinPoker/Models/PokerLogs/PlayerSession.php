<?php

namespace App\BackOffice\CoinPoker\Models\PokerLogs;

class PlayerSession extends PokerLogsModel
{
    protected $table = 'player_session';

    protected $fillable = [
        'player_session_id',
        'franchise_id',
        'affiliate_id',
        'operation_id',
        'table_id',
        'player_id',
        'game_ssid', // This is the source of table_session_id used for Poker Game Check
        'player_name',
        'stake_desc',
        'game_desc',
        'currency_symbol',
        'from_date',
        'to_date',
        'leave_stack',
        'hands_count',
        'hands_won',
        'total_bets',
        'total_result',
        'total_won',
        'total_rake',
        'hands_raked',
    ];

    /**
     * Relationship to PlayerAccount in gaming_system database
     */
    public function playerAccount()
    {
        return $this->belongsTo(
            \App\BackOffice\CoinPoker\Models\GamingSystem\PlayerAccount::class,
            'player_id',
            'player_id'
        );
    }

    /**
     * Relationship to Provider through PlayerAccount
     */
    public function provider()
    {
        return $this->hasOneThrough(
            \App\BackOffice\CoinPoker\Models\GamingSystem\Provider::class,
            \App\BackOffice\CoinPoker\Models\GamingSystem\PlayerAccount::class,
            'player_id', // Foreign key on PlayerAccount table
            'id', // Foreign key on Provider table
            'player_id', // Local key on PlayerSession table
            'provider_id' // Local key on PlayerAccount table
        );
    }
}
