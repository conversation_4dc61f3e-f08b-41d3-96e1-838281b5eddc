<?php

namespace App\BackOffice\CoinPoker\Models\CoinPoker;

use App\BackOffice\CoinPoker\Models\GamingSystem\PlayerTransaction;
use Illuminate\Database\Eloquent\Model;

class Player extends Model
{
    protected $connection = 'coinpoker.coin_poker';

    protected $table = 'player';

    protected $fillable = [
        'email',
        'password_hash',
        'nickname',
        'balance_usdt',
        'balance_chp',
        'confirmed_at',
        'reconfirm_at',
        'terms_of_service',
        'newsletter',
        'banned',
        'affiliate',
        'affiliate_id',
        'affiliate_token',
        'password_reset_token',
        'auto_confirms',
        'agent_player_id',
        'referral_code',
        'referrer_id',
    ];

    protected $casts = [
        'balance_usdt' => 'decimal:6',
        'balance_chp' => 'decimal:18',
        'terms_of_service' => 'boolean',
        'newsletter' => 'boolean',
        'banned' => 'boolean',
        'created_at' => 'datetime',
        'confirmed_at' => 'datetime',
        'reconfirm_at' => 'datetime',
    ];

    public function transactions()
    {
        return $this->hasMany(PlayerTransaction::class);
    }

    public function referrer()
    {
        return $this->belongsTo(Player::class, 'referrer_id');
    }

    public function referrals()
    {
        return $this->hasMany(Player::class, 'referrer_id');
    }
}
