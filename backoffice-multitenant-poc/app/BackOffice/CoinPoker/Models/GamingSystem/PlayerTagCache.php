<?php

namespace App\BackOffice\CoinPoker\Models\GamingSystem;

class PlayerTagCache extends GamingSystemModel
{
    protected $table = 'player_tag_cache';

    protected $primaryKey = 'player_id';

    public $incrementing = false;

    protected $fillable = [
        'player_id',
        'tags',
    ];

    /**
     * Get the player associated with this tag cache
     */
    public function player()
    {
        return $this->belongsTo(PlayerAccount::class, 'player_id', 'player_id');
    }
} 