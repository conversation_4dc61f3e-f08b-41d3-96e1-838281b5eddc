<?php

namespace App\BackOffice\CoinPoker\Models\GamingSystem;

class PlayerSession extends GamingSystemModel
{
    protected $table = 'player_session';

    public $timestamps = false;

    protected $fillable = [
        'player_id',
        'client_session_id',
        'proxy_id',
        'ip',
        'port',
        'app_platform',
        'app_platform_ex',
        'app_version',
        'app_session_id',
        'login_date',
        'logout_date',
        'disconnect_time',
    ];

    public function playerAccount()
    {
        return $this->belongsTo(PlayerAccount::class, 'player_id', 'player_id');
    }
}
