<?php

namespace App\BackOffice\CoinPoker\Models\GamingSystem;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AntiCheatHeatmap extends GamingSystemModel
{
    protected $table = 'anti_cheat_heatmap';

    public $timestamps = false;

    protected $primaryKey = 'anti_cheat_heatmap_id';

    protected $fillable = [
        'anti_cheat_heatmap_id',
        'player_id',
        'create_date',
        'eval_result',
        'total_points',
        'points',
        'game_type',
        'game_event_type',
        'tour_id',
        'table_id',
    ];

    public function playerAccount(): BelongsTo
    {
        return $this->belongsTo(PlayerAccount::class, 'player_id', 'player_id');
    }

    public function getBlobData(): string
    {
        return $this->points;
    }

    public function hasValidBlobData(): bool
    {
        return ! empty($this->points);
    }

    public function getGameTypeDisplay(): string
    {
        return $this->game_type == 0 ? 'OFC' : 'Poker';
    }

    public function getGameEventTypeDisplay(): string
    {
        return $this->game_event_type == 0 ? 'Regular' : 'Tournament';
    }
}
