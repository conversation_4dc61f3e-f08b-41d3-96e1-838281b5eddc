<?php

namespace App\BackOffice\CoinPoker\Models\GamingSystem;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OfcSeatCaptcha extends GamingSystemModel
{
    protected $table = 'ofc_seat_captcha';

    public $timestamps = false;

    protected $primaryKey = 'captcha_id';

    protected $fillable = [
        'captcha_id',
        'player_id',
        'entry_fail',
        'verified',
        'pending',
        'date_created',
        'date_verified',
    ];

    public function playerAccount(): BelongsTo
    {
        return $this->belongsTo(PlayerAccount::class, 'player_id', 'player_id');
    }
}
