<?php

namespace App\BackOffice\CoinPoker\Models\GamingSystem;

class RestrictionGroupPlayer extends GamingSystemModel
{
    protected $table = 'restriction_group_player';

    public $timestamps = false;

    protected $fillable = [
        'group_id',
        'player_id',
    ];

    public function group()
    {
        return $this->belongsTo(RestrictionGroup::class, 'group_id', 'id');
    }

    public function player()
    {
        return $this->belongsTo(PlayerAccount::class, 'player_id', 'player_id');
    }
}
