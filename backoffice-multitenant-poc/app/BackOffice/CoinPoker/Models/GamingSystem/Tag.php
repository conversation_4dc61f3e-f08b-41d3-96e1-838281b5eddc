<?php

namespace App\BackOffice\CoinPoker\Models\GamingSystem;

use App\BackOffice\CoinPoker\Models\GamingSystem\PlayerAccount;

class Tag extends GamingSystemModel
{
    protected $table = 'tag';

    protected $fillable = [
        'name',
        'color',
    ];

    /**
     * Get the players that have this tag
     */
    public function players()
    {
        return $this->belongsToMany(PlayerAccount::class, 'player_tag', 'tag_id', 'player_id');
    }
} 