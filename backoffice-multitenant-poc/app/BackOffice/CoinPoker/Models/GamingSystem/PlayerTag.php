<?php

namespace App\BackOffice\CoinPoker\Models\GamingSystem;

use App\BackOffice\CoinPoker\Models\GamingSystem\PlayerAccount;

/**
 * PlayerTag is a pivot model for the many-to-many relationship between players and tags
 */
class PlayerTag extends GamingSystemPivot
{
    protected $table = 'player_tag';

    public $timestamps = false;

    protected $fillable = [
        'player_id',
        'tag_id',
    ];

    /**
     * Get the player associated with this tag assignment
     */
    public function player()
    {
        return $this->belongsTo(PlayerAccount::class, 'player_id', 'player_id');
    }

    /**
     * Get the tag associated with this player assignment
     */
    public function tag()
    {
        return $this->belongsTo(Tag::class, 'tag_id', 'id');
    }
} 