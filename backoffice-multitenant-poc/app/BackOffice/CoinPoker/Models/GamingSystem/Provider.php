<?php

namespace App\BackOffice\CoinPoker\Models\GamingSystem;

class Provider extends GamingSystemModel
{
    public const string COINPOKER_PROVIDER_NAME = 'CoinPoker';

    protected $table = 'provider';

    public $timestamps = false;

    protected $fillable = [
        'id',
        'network_id',
        'provider_name',
        'api_authentication_key',
    ];

    public function playerAccounts()
    {
        return $this->hasMany(PlayerAccount::class, 'provider_id', 'id');
    }
}
