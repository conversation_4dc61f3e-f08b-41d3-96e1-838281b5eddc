<?php

namespace App\BackOffice\CoinPoker\Models\GamingSystem;

class RestrictionGroup extends GamingSystemModel
{
    protected $table = 'restriction_group';

    protected $primaryKey = 'id';

    public $timestamps = false;

    protected $fillable = [
        'name',
    ];

    public function players()
    {
        return $this->belongsToMany(PlayerAccount::class, 'restriction_group_player', 'group_id', 'player_id');
    }
}
