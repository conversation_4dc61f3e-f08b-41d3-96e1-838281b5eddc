<?php

namespace App\BackOffice\CoinPoker\Models\GamingSystem;

class PlayerTransaction extends GamingSystemModel
{
    protected $table = 'player_transaction';

    protected $primaryKey = 'player_trans_id';

    protected $fillable = [
        'player_id',
        'session_id',
        'client_platform',
        'table_session_id', // This is used to get the logs from the hand_log table
        'table_id',
        'tour_id',
        'game_mode_name',
        'description',
        'game_event_type',
        'game_type',
        'buyin_date',
        'buyin_type',
        'buyin',
        'tour_fee',
        'knockout',
        'buyout_type',
        'payout',
        'cash_rake',
        'place',
        'bounty',
        'target_tour_id',
    ];

    protected $casts = [
        'buyin_date' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function playerAccount()
    {
        return $this->belongsTo(PlayerAccount::class, 'player_id', 'player_id');
    }

    public function player()
    {
        return $this->playerAccount();
    }

    public function scopeFromPlayer($builder, $playerId)
    {
        return $builder->where('player_id', $playerId);
    }
}
