<?php

namespace App\BackOffice\CoinPoker\Models\GamingSystem;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Company extends GamingSystemModel
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'company_list';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'company_id';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'company_name',
        'company_short_name',
        'company_currency',
        'company_country',
        'company_language',
        'company_timezone',
        'company_status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'company_id' => 'integer',
        'company_currency' => 'integer',
        'company_country' => 'integer',
        'company_language' => 'integer',
        'company_status' => 'integer',
    ];

    /**
     * Get companies as key-value pairs for dropdowns
     *
     * @return array
     */
    public static function getCompaniesDropdown(): array
    {
        return self::pluck('company_name', 'company_id')->toArray();
    }

    /**
     * Get company name by ID
     *
     * @param int $companyId
     * @return string|null
     */
    public static function getCompanyName(int $companyId): ?string
    {
        return self::where('company_id', $companyId)->value('company_name');
    }

    /**
     * Scope for active companies
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeActive($query)
    {
        return $query->where('company_status', 1);
    }
} 