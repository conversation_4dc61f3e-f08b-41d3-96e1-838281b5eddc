<?php

namespace App\BackOffice\CoinPoker\Models\GamingSystem;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PlayerAccount extends GamingSystemModel
{
    protected $table = 'player_account';

    public $timestamps = false;

    protected $primaryKey = 'player_id';

    protected $fillable = [
        'player_id',
        'provider_id',
        'player_provider_id',
        'player_session_id',
        'previous_session_id',
        'nick_name',
        'player_avatar',
        'country_id',
        'currency_id',
        'language_id',
        'cur_proxy_id',
        'cur_ip',
        'cur_port',
        'first_login',
        'last_login',
        'last_update',
        'login_count',
        'crash_count',
        'balance_play',
        'balance_money',
        'balance_total',
        'client_ipaddress',
        'client_macaddress',
        'collusion_check',
        'chat_blocked',
        'banned',
        'franchise_id',
        'vhost',
        'client_platform',
        'client_version',
        'table_mask',
        'version_string',
        'player_currency_code',
        'player_currency_usable',
        'player_currency_total',
        'company_id',
        'email',
        'constant_screenshoting',
        'is_test_account',
        'provider_nick_name',
    ];

    public function playerSessions(): HasMany
    {
        return $this->hasMany(PlayerSession::class, 'player_id', 'player_id');
    }

    public function provider(): BelongsTo
    {
        return $this->belongsTo(Provider::class, 'provider_id', 'id');
    }

    public function restrictionGroups(): BelongsToMany
    {
        return $this->belongsToMany(RestrictionGroup::class, 'restriction_group_player', 'player_id', 'group_id');
    }

    public function ofcSeatCaptchas(): HasMany
    {
        return $this->hasMany(OfcSeatCaptcha::class, 'player_id', 'player_id');
    }

    public function antiCheatHeatmaps(): HasMany
    {
        return $this->hasMany(AntiCheatHeatmap::class, 'player_id', 'player_id');
    }
}
