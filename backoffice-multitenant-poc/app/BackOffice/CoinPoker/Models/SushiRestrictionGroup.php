<?php

namespace App\BackOffice\CoinPoker\Models;

use Illuminate\Database\Eloquent\Model;
use Sushi\Sushi;

class SushiRestrictionGroup extends Model
{
    use Sushi;

    protected $casts = [
        'name' => 'string',
        'players' => 'json',
    ];

    protected static array $injectedRows = [];

    public function getRows(): array
    {
        return self::$injectedRows ?? [];
    }

    public static function addRow($row): self
    {
        if (is_array($row['players'] ?? [])) {
            $row['players'] = json_encode($row['players']);
        }
        self::$injectedRows[] = $row;

        return new self;
    }

    // @todo: check if the casting with sushi works properly
    public function getPlayersAttribute()
    {
        return json_decode($this->attributes['players'], true);
    }
}
