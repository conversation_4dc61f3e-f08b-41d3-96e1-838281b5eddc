<?php

namespace App\BackOffice\CoinPoker\Models\Ofcp;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class TournamentPlayerOverlay extends OfcpModel
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'b_ofcp_tournament_player_overlay';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'tour_id',
        'player_id',
        'prize',
        'overlay',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'tour_id' => 'integer',
        'player_id' => 'integer',
        'prize' => 'integer',
        'overlay' => 'integer',
    ];
} 