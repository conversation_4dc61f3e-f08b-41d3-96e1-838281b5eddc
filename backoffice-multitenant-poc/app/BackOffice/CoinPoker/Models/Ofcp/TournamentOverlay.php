<?php

namespace App\BackOffice\CoinPoker\Models\Ofcp;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class TournamentOverlay extends OfcpModel
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'b_ofcp_tournament_overlay';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'tour_id',
        'game_type',
        'game_event_type',
        'buyin',
        'fee',
        'rebuy',
        'rebuy_fee',
        'rebuy_count',
        'addon',
        'addon_fee',
        'addon_count',
        'prize',
        'overlay',
        'log',
        'tgbet_synced',
        'started_at',
        'finished_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'tour_id' => 'integer',
        'game_type' => 'integer',
        'game_event_type' => 'integer',
        'buyin' => 'integer',
        'fee' => 'integer',
        'rebuy' => 'integer',
        'rebuy_fee' => 'integer',
        'rebuy_count' => 'integer',
        'addon' => 'integer',
        'addon_fee' => 'integer',
        'addon_count' => 'integer',
        'prize' => 'integer',
        'overlay' => 'integer',
        'tgbet_synced' => 'boolean',
        'started_at' => 'datetime',
        'finished_at' => 'datetime',
        'updated_at' => 'datetime',
        'created_at' => 'datetime',
    ];
} 