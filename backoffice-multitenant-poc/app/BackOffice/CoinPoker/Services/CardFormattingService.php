<?php

namespace App\BackOffice\CoinPoker\Services;

class CardFormattingService
{
    /**
     * Card definitions in order: Spades, Clubs, Diamonds, Hearts
     */
    private const CARDS = [
        'As', 'Ks', 'Qs', 'Js', 'Ts', '9s', '8s', '7s', '6s', '5s', '4s', '3s', '2s',
        'Ac', 'Kc', 'Qc', 'Jc', 'Tc', '9c', '8c', '7c', '6c', '5c', '4c', '3c', '2c',
        'Ad', 'Kd', 'Qd', 'Jd', 'Td', '9d', '8d', '7d', '6d', '5d', '4d', '3d', '2d',
        'Ah', 'Kh', 'Qh', 'Jh', 'Th', '9h', '8h', '7h', '6h', '5h', '4h', '3h', '2h',
    ];

    /**
     * Format log text by converting card codes to HTML with CSS classes
     * Replicates the Drupal _format_log function
     */
    public function formatLog(string $text): string
    {
        // Create HTML span tags for each card
        $tags = array_map(function ($card) {
            return '<span class="card-img img'.$card.'"></span>';
        }, self::CARDS);

        // Escape HTML characters first
        $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');

        // Create callback function for regex replacement
        $callback = function (array $matches) use ($tags) {
            return str_replace(self::CARDS, $tags, $matches[0]);
        };

        // Replace card references in brackets like [As Kh]
        $text = preg_replace_callback('/\[.+\]/', $callback, $text);

        // Replace card references in lines containing "cards:"
        $text = preg_replace_callback('/.*cards:.*/', $callback, $text);

        // Convert line breaks to HTML
        return nl2br($text);
    }

    /**
     * Format multiple log entries
     */
    public function formatLogs(array $logs): string
    {
        $html = '';

        foreach ($logs as $log) {
            $text = $log['text'] ?? $log->text ?? '';
            $html .= $this->formatLog($text);
        }

        return $html;
    }
}
