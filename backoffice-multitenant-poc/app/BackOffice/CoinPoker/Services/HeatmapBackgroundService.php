<?php

namespace App\BackOffice\CoinPoker\Services;

use App\BackOffice\CoinPoker\Models\GamingSystem\AntiCheatHeatmap;
use App\BackOffice\CoinPoker\Models\GamingSystem\CardHistoryTourList;
use App\BackOffice\CoinPoker\Models\GamingSystem\CardRegularList;
use App\BackOffice\CoinPoker\Models\GamingSystem\CardTourList;
use App\BackOffice\CoinPoker\Models\GamingSystem\PokerHistoryTourList;
use App\BackOffice\CoinPoker\Models\GamingSystem\PokerRingList;
use App\BackOffice\CoinPoker\Models\GamingSystem\PokerTourList;

class HeatmapBackgroundService
{
    public function getBackgroundImage(AntiCheatHeatmap $heatmap): string
    {
        $gameType = $heatmap->game_type;
        $gameEventType = $heatmap->game_event_type;
        $tableId = $heatmap->table_id;
        $tourId = $heatmap->tour_id;

        $seatCount = $this->determineSeatCount($gameType, $gameEventType, $tableId, $tourId);

        if ($gameType == 0) {
            return "ofc_{$seatCount}.png";
        } else {
            return "poker_{$seatCount}.png";
        }
    }

    private function determineSeatCount(int $gameType, int $gameEventType, ?int $tableId, ?int $tourId): int
    {
        if ($gameType == 0) {
            if ($gameEventType == 0) {
                $seatCount = CardRegularList::where('card_regular_list_id', intval($tableId / 10))
                    ->value('seat_count');

                return $seatCount ?: 3;
            } else {
                $seatCount = CardHistoryTourList::where('card_tour_list_id', $tourId)
                    ->value('seats_per_table');

                if (! $seatCount) {
                    $seatCount = CardTourList::where('card_tour_list_id', $tourId)
                        ->value('seats_per_table');
                }

                return $seatCount ?: 3;
            }
        } else {
            if ($gameEventType == 0) {
                $seatCount = PokerRingList::where('poker_ring_list_id', intval($tableId / 10))
                    ->value('seats_per_table');

                return $seatCount ?: 7;
            } else {
                $seatCount = PokerHistoryTourList::where('poker_tour_list_id', $tourId)
                    ->value('seats_per_table');

                if (! $seatCount) {
                    $seatCount = PokerTourList::where('poker_tour_list_id', $tourId)
                        ->value('seats_per_table');
                }

                return $seatCount ?: 7;
            }
        }
    }

    public function getBackgroundPath(string $backgroundImage): string
    {
        return config('coinpoker.heatmap.backgrounds_path')."/{$backgroundImage}";
    }
}
