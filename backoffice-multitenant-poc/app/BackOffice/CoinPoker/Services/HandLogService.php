<?php

namespace App\BackOffice\CoinPoker\Services;

use DateTime;
use DateTimeZone;
use Illuminate\Support\Facades\Log;
use MongoDB\BSON\Javascript;
use MongoDB\BSON\Regex;
use MongoDB\BSON\UTCDateTime;
use MongoDB\Client;
use MongoDB\Driver\Command;

class HandLogService
{
    private Client $client;

    public function __construct()
    {
        $uri = config('services.mongodb.hand_log_uri');
        if (! $uri) {
            throw new \Exception('MongoDB Hand Log URI not configured');
        }

        // MongoDB 3.6.3 compatibility options
        $options = [
            'connectTimeoutMS' => 10000,
            'serverSelectionTimeoutMS' => 10000,
            'socketTimeoutMS' => 30000,
            'retryWrites' => false, // Not supported in 3.6.3
            'retryReads' => false,  // Not supported in 3.6.3
            'w' => 1,               // Write concern
            'authSource' => 'admin', // Use admin as auth source to access multiple databases
            'ssl' => false,         // Disable SSL unless explicitly needed
            'readPreference' => 'primary',
        ];

        $this->client = new Client($uri, $options);
    }

    public function find(array $params): \Traversable
    {
        $collection = $this->client->poker->hands;

        return $collection->find($this->_buildQuery($params));
    }

    public function findOfcHandById(int $id): \Traversable
    {
        $collection = $this->client->ofc->hands;

        return $collection->find(['_id' => $id]);
    }

    public function find_by_table_session(string $table_session): \Traversable
    {
        return $this->find(['table_session' => $table_session]);
    }

    private function _buildQuery(array $params): array
    {
        $q = [];

        foreach ($params as $key => $param) {
            if ($param == null || $param == '') {
                continue;
            }

            switch ($key) {
                case 'from_date':
                    $q['created_at']['$gte'] = new UTCDateTime(new DateTime($param, new DateTimeZone('UTC')));
                    break;

                case 'to_date':
                    $q['created_at']['$lte'] = new UTCDateTime(new DateTime($param, new DateTimeZone('UTC')));
                    break;

                case 'hand_id':
                    $q['_id'] = (int) $param;
                    break;

                case 'player_id':
                    $ids = explode(',', $param);
                    $q['$and'] = [];
                    foreach ($ids as $id) {
                        $q['$and'][] = ['players.id' => (int) $id];
                    }
                    break;

                case 'table_id':
                    $q['table_id'] = (int) $param;
                    break;

                case 'tour_id':
                    $q['tour_id'] = (int) $param;
                    break;

                case 'table_session':
                    $q['players.table_session'] = $param;
                    break;
            }
        }

        return $q;
    }

    public function generateCashTables(DateTime $from, DateTime $to): void
    {
        $cmd = [
            'mapReduce' => 'hands',

            'query' => [
                'tour_id' => 0, // cash only
                'created_at' => [
                    '$gte' => new UTCDateTime($from),
                    '$lt' => new UTCDateTime($to),
                ],
            ],

            'out' => [
                'merge' => 'cash_tables',
                'db' => 'reports',
            ],

            'map' => "
                function() {
                    const hand = parseHand(this.text);
                    if(hand.errors.length)
                        return;

                    hand.date.setUTCHours(0, 0, 0, 0);
                    const key = {
                        type: hand.type,
                        stake: hand.small_blind.toFixed(2) + '/' + hand.big_blind.toFixed(2),
                        date: hand.date
                    };

                    const rake = hand.players.reduce((acc, current) => acc + current.rake, 0);

                    const value = {
                        duration: hand.duration,
                        rake: rake,
                        hand_count: 1
                    };

                    emit(key, value);
                }
            ",

            'reduce' => '
                function (key, values) {
                    return values.reduce((accumulator, value) => {
                        return {
                            duration: accumulator.duration + value.duration,
                            rake: accumulator.rake + value.rake,
                            hand_count: accumulator.hand_count + value.hand_count
                        };
                    },
                    { duration: 0, rake: 0, hand_count: 0 }
                    );
                }
            ',
        ];

        $this->client->getManager()->executeCommand('poker', new Command($cmd));
    }

    public function getCashTablesReport(DateTime $from, DateTime $to, ?string $type, ?string $stake): \Traversable
    {
        $from->setTime(0, 0, 0, 0);
        $to->setTime(0, 0, 0, 0);

        $query = [
            '_id.date' => [
                '$gte' => new UTCDateTime($from),
                '$lt' => new UTCDateTime($to),
            ],
        ];

        if ($type) {
            $query['_id.type'] = new Regex($type);
        }

        if ($stake) {
            $query['_id.stake'] = new Regex($stake);
        }

        return $this->client->reports->cash_tables->aggregate([
            ['$match' => $query],
            [
                '$group' => [
                    '_id' => ['type' => '$_id.type', 'stake' => '$_id.stake'],
                    'duration' => ['$sum' => '$value.duration'],
                    'hand_count' => ['$sum' => '$value.hand_count'],
                    'rake' => ['$sum' => '$value.rake'],
                ],
            ],
            ['$sort' => ['hand_count' => -1]],
        ]);
    }

    public function generateCashSessions(DateTime $from, DateTime $to): void
    {
        $cmd = [
            'mapReduce' => 'hands',

            'query' => [
                'tour_id' => 0, // cash only
                'created_at' => [
                    '$gte' => new UTCDateTime($from),
                    '$lt' => new UTCDateTime($to),
                ],
            ],

            'out' => [
                'reduce' => 'cash_sessions',
                'db' => 'reports',
            ],

            'map' => "
                function() {
                    const hand = parseHand(this.text);

                    hand.players.forEach(player => {
                        Object.keys(player.opponents).forEach(id => player.opponents[id]['hand_count'] = 1);
                        emit(player.table_session, {
                            player_id: player.id,
                            tid: hand.tid,
                            bet: player.bet,
                            rake: player.rake,
                            rake_unsplit: player._rake,
                            won: player.won,
                            hand_count: 1,
                            opponents: player.opponents
                        });
                    });
                }
            ",

            'reduce' => $this->getCashSessionsAggregateReduceFunction(),
        ];

        $this->client->getManager()->executeCommand('poker', new Command($cmd));
    }

    public function updateCashSessionBuyinDate(string $session, DateTime $buyinDate): void
    {
        $collection = $this->client->reports->cash_sessions;
        $collection->updateOne(
            ['_id' => $session],
            ['$set' => ['buyin_date' => new UTCDateTime($buyinDate)]]
        );
    }

    public function getCashSessionsGroupedByTable(DateTime $from, DateTime $to, int $playerId): array
    {
        $cmd = [
            'mapReduce' => 'cash_sessions',
            'query' => [
                'buyin_date' => [
                    '$gte' => new UTCDateTime($from),
                    '$lt' => new UTCDateTime($to),
                ],
                'value.player_id' => $playerId,
            ],
            'out' => ['inline' => 1],
            'map' => '
                function() {
                    emit(this.value.tid, this.value);
                }
            ',

            'reduce' => $this->getCashSessionsAggregateReduceFunction(),
        ];

        return $this->client->getManager()
            ->executeCommand('reports', new Command($cmd))
            ->toArray()[0]
            ->results;
    }

    private function getCashSessionsAggregateReduceFunction(): Javascript
    {
        return new Javascript(
            'function (session, hands) {
                return hands.reduce((acc, hand) => {

                    // merge opponents
                    Object.keys(hand.opponents).forEach(playerId => {
                        if(acc.opponents.hasOwnProperty(playerId)) {
                            acc.opponents[playerId].won += hand.opponents[playerId].won;
                            acc.opponents[playerId].lost += hand.opponents[playerId].lost;
                            acc.opponents[playerId].hand_count += hand.opponents[playerId].hand_count;
                        } else {
                            acc.opponents[playerId] = hand.opponents[playerId];
                        }
                    });

                    return {
                        player_id: NumberInt(hand.player_id),
                        tid: NumberInt(hand.tid),
                        bet: acc.bet + hand.bet,
                        rake: acc.rake + hand.rake,
                        rake_unsplit: acc.rake_unsplit + hand.rake_unsplit,
                        won: acc.won + hand.won,
                        hand_count: acc.hand_count + hand.hand_count,
                        opponents: acc.opponents
                    };
                },
                { player_id: 0, tid: 0, bet: 0, rake: 0, rake_unsplit: 0, won: 0, hand_count: 0, opponents: {} }
                );
            }'
        );
    }

    public function getCashTimePlayedAndHandCount(DateTime $from, DateTime $to, $playerId): \stdClass
    {
        $collection = $this->client->poker->hands;
        $result = $collection->find([
            'players.id' => $playerId,
            'created_at' => [
                '$gte' => new UTCDateTime($from),
                '$lt' => new UTCDateTime($to),
            ],
            'tour_id' => 0,
        ],
            [
                'projection' => ['text' => 1],
                'sort' => ['created_at' => 1],
                'maxTimeMS' => 1000 * 60 * 30,
            ]
        );

        $ranges = [];
        $previous = null;
        $count = 0;
        foreach ($result as $doc) {
            $begin = $this->_findDate('/^Hand .* (\S+ \S+) GMT$/m', $doc->text)->getTimestamp();
            $end = $this->_findDate('/^Game ended: (\S+ \S+) GMT$/m', $doc->text)->getTimestamp();
            $count++;

            if (! $ranges) {
                $ranges[] = [$begin, $end];
                $previous = &$ranges[0];

                continue;
            }

            if ($begin > $previous[1]) {
                $ranges[] = [$begin, $end];
                $previous = &$ranges[count($ranges) - 1];
            } elseif ($end > $previous[1]) {
                $previous[1] = $end;
            }
        }

        $ret = new \stdClass;
        $ret->handCount = $count;
        $ret->timePlayed = 0;

        foreach ($ranges as $range) {
            $ret->timePlayed += $range[1] - $range[0];
        }

        return $ret;
    }

    private function _findDate($regexp, $text): DateTime
    {
        $match = [];
        preg_match($regexp, $text, $match);
        if ($match) {
            return new DateTime($match[1], new DateTimeZone('GMT'));
        }

        throw new \Exception('Unable to match date: '.$text);
    }

    /**
     * Check if MongoDB connection is available.
     */
    public function isAvailable(): bool
    {
        try {
            // Test connection with ping command
            $result = $this->client->selectDatabase('admin')->command(['ping' => 1]);
            $pingResult = $result->toArray();

            if (empty($pingResult) || $pingResult[0]['ok'] != 1) {
                Log::warning('MongoDB ping failed');

                return false;
            }

            return true;
        } catch (\Exception $e) {
            Log::warning('MongoDB Hand Log is not available: '.$e->getMessage());

            return false;
        }
    }

    /**
     * Get MongoDB version information
     */
    public function getVersionInfo(): array
    {
        try {
            $result = $this->client->selectDatabase('admin')->command(['buildInfo' => 1]);
            $buildInfo = $result->toArray()[0];

            return [
                'version' => $buildInfo['version'] ?? 'unknown',
                'gitVersion' => $buildInfo['gitVersion'] ?? 'unknown',
                'compatible' => version_compare($buildInfo['version'], '3.6.0', '>=') &&
                              version_compare($buildInfo['version'], '4.0.0', '<'),
            ];
        } catch (\Exception $e) {
            return [
                'version' => 'unknown',
                'error' => $e->getMessage(),
                'compatible' => false,
            ];
        }
    }

    /**
     * Get collection stats for debugging.
     */
    public function getStats(): array
    {
        try {
            // Use countDocuments if available, otherwise fall back to count for 3.6.3
            $pokerCollection = $this->client->poker->hands;
            $ofcCollection = $this->client->ofc->hands;

            $pokerStats = method_exists($pokerCollection, 'countDocuments')
                ? $pokerCollection->countDocuments([])
                : $pokerCollection->count();

            $ofcStats = method_exists($ofcCollection, 'countDocuments')
                ? $ofcCollection->countDocuments([])
                : $ofcCollection->count();

            return [
                'connected' => true,
                'poker_hands_count' => $pokerStats,
                'ofc_hands_count' => $ofcStats,
                'version_info' => $this->getVersionInfo(),
            ];
        } catch (\Exception $e) {
            return [
                'connected' => false,
                'error' => $e->getMessage(),
                'version_info' => $this->getVersionInfo(),
            ];
        }
    }

    /**
     * Test connection with common query patterns
     */
    public function testConnection(): array
    {
        $results = [
            'connection' => false,
            'version' => null,
            'collections' => [],
            'sample_queries' => [],
        ];

        try {
            // Test basic connection
            $results['connection'] = $this->isAvailable();

            if (! $results['connection']) {
                return $results;
            }

            // Get version info
            $results['version'] = $this->getVersionInfo();

            // Test collection access
            $pokerCollection = $this->client->poker->hands;
            $ofcCollection = $this->client->ofc->hands;

            $results['collections'] = [
                'poker_hands' => method_exists($pokerCollection, 'countDocuments')
                    ? $pokerCollection->countDocuments([])
                    : $pokerCollection->count(),
                'ofc_hands' => method_exists($ofcCollection, 'countDocuments')
                    ? $ofcCollection->countDocuments([])
                    : $ofcCollection->count(),
            ];

            // Test sample queries
            $results['sample_queries'] = [
                'basic_find' => $this->testBasicFind(),
                'date_range' => $this->testDateRangeQuery(),
                'player_query' => $this->testPlayerQuery(),
            ];

        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    private function testBasicFind(): array
    {
        try {
            $results = $this->find(['limit' => 1]);
            $count = 0;
            foreach ($results as $result) {
                $count++;
                if ($count >= 1) {
                    break;
                }
            }

            return ['success' => true, 'count' => $count];
        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    private function testDateRangeQuery(): array
    {
        try {
            $from = new DateTime('2024-01-01', new DateTimeZone('UTC'));
            $to = new DateTime('2024-01-02', new DateTimeZone('UTC'));

            $results = $this->find([
                'from_date' => $from->format('Y-m-d H:i:s'),
                'to_date' => $to->format('Y-m-d H:i:s'),
                'limit' => 1,
            ]);

            $count = 0;
            foreach ($results as $result) {
                $count++;
                if ($count >= 1) {
                    break;
                }
            }

            return ['success' => true, 'count' => $count];
        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    private function testPlayerQuery(): array
    {
        try {
            $results = $this->find(['player_id' => '1', 'limit' => 1]);
            $count = 0;
            foreach ($results as $result) {
                $count++;
                if ($count >= 1) {
                    break;
                }
            }

            return ['success' => true, 'count' => $count];
        } catch (\Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Find hand by ID and return the text content (replicates Drupal poker_game_check_find_hand_submit)
     *
     * @return array|null Returns the hand document or null if not found
     */
    public function findHandById(int $handId): ?array
    {
        try {
            $collection = $this->client->poker->hands;
            $hand = $collection->findOne(['_id' => $handId]);

            if (! $hand) {
                return null;
            }

            // Convert MongoDB document to array
            return json_decode(json_encode($hand), true);

        } catch (\Exception $e) {
            Log::error('Error finding hand by ID: '.$e->getMessage(), [
                'hand_id' => $handId,
                'exception' => $e,
            ]);
            throw $e;
        }
    }

    /**
     * Find hand by ID and return only the text content (replicates Drupal poker_game_check_find_hand_submit)
     *
     * @return string|null Returns the hand text or null if not found
     */
    public function findHandTextById(int $handId): ?string
    {
        try {
            $collection = $this->client->poker->hands;
            $hand = $collection->findOne(
                ['_id' => $handId],
                ['projection' => ['text' => 1]]
            );

            if (! $hand) {
                return null;
            }

            return $hand->text ?? null;

        } catch (\Exception $e) {
            Log::error('Error finding hand text by ID: '.$e->getMessage(), [
                'hand_id' => $handId,
                'exception' => $e,
            ]);
            throw $e;
        }
    }

    /**
     * Test the findHandById method with a sample hand ID
     *
     * @return array Test results
     */
    public function testFindHandById(): array
    {
        $results = [
            'success' => false,
            'hand_found' => false,
            'hand_id' => null,
            'error' => null,
            'sample_hand' => null,
        ];

        try {
            // First, let's try to find a sample hand to test with
            $collection = $this->client->poker->hands;
            $sampleHand = $collection->findOne([], ['projection' => ['_id' => 1]]);

            if (! $sampleHand) {
                $results['error'] = 'No hands found in database';

                return $results;
            }

            $testHandId = $sampleHand->_id;
            $results['hand_id'] = $testHandId;

            // Test the findHandById method
            $hand = $this->findHandById($testHandId);

            if ($hand) {
                $results['success'] = true;
                $results['hand_found'] = true;
                $results['sample_hand'] = [
                    'id' => $hand['_id'] ?? null,
                    'has_text' => isset($hand['text']),
                    'text_length' => strlen($hand['text'] ?? ''),
                    'created_at' => $hand['created_at'] ?? null,
                ];
            } else {
                $results['error'] = 'Hand not found with ID: '.$testHandId;
            }

        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }
}
