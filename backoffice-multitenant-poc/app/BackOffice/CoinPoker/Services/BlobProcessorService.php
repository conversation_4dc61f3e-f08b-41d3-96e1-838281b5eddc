<?php

namespace App\BackOffice\CoinPoker\Services;

use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;

class BlobProcessorService
{
    private string $binaryPath;

    public function __construct()
    {
        $this->binaryPath = config('coinpoker.heatmap.binary_path');
    }

    public function generateHeatmapImage(string $blobData, string $backgroundImagePath): string
    {
        try {
            if ($this->isBinaryAvailable()) {
                return $this->generateWithBinary($blobData, $backgroundImagePath);
            }
        } catch (\Exception $exception) {
            Log::warning('Binary heatmap generation failed, falling back to PHP', [
                'error' => $exception->getMessage(),
            ]);
        }

        // Fallback to pure PHP implementation
        return $this->generateWithPhp($blobData, $backgroundImagePath);
    }

    private function generateWithBinary(string $blobData, string $backgroundImagePath): string
    {
        $tempBlobFile = tempnam(sys_get_temp_dir(), 'heatmap_blob_');
        $tempOutputFile = tempnam(sys_get_temp_dir(), 'heatmap_output_');

        try {
            file_put_contents($tempBlobFile, $blobData);

            $process = new Process([
                $this->binaryPath,
                '-i', $tempBlobFile,
                '-o', $tempOutputFile,
                '-b', $backgroundImagePath,
                '-v',
            ]);

            $process->run();

            if (! $process->isSuccessful()) {
                Log::error('Heatmap binary process failed', [
                    'command' => $process->getCommandLine(),
                    'output' => $process->getOutput(),
                    'error' => $process->getErrorOutput(),
                    'exit_code' => $process->getExitCode(),
                ]);

                throw new ProcessFailedException($process);
            }

            if (! file_exists($tempOutputFile) || filesize($tempOutputFile) === 0) {
                throw new \Exception('Binary process completed but no output image was generated');
            }

            $imageContent = file_get_contents($tempOutputFile);

            if ($imageContent === false) {
                throw new \Exception('Failed to read generated image file');
            }

            return $imageContent;

        } finally {
            if (file_exists($tempBlobFile)) {
                unlink($tempBlobFile);
            }
            if (file_exists($tempOutputFile)) {
                unlink($tempOutputFile);
            }
        }
    }

    private function generateWithPhp(string $blobData, string $backgroundImagePath): string
    {
        $generator = new PhpHeatmapGenerator($backgroundImagePath);

        return $generator->generateHeatmap($blobData);
    }

    public function isBinaryAvailable(): bool
    {
        if (! file_exists($this->binaryPath) || ! is_executable($this->binaryPath)) {
            return false;
        }

        // Test if binary can actually run on this system
        $process = new Process([$this->binaryPath, '--version'], null, null, null, 1);
        $process->run();

        return $process->getExitCode() !== 5; // Signal 5 = SIGTRAP/segfault
    }
}
