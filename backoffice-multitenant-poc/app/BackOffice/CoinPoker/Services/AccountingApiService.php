<?php

namespace App\BackOffice\CoinPoker\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AccountingApiService
{
    protected string $baseUrl;
    protected string $apiToken;

    public function __construct()
    {
        $this->baseUrl = config('services.coinpoker.accounting_api.api_url');
        $this->apiToken = config('services.coinpoker.accounting_api.api_token');
    }

    /**
     * Get common HTTP headers for API requests
     */
    private function getHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiToken,
            'Content-Type' => 'application/json',
        ];
    }

    /**
     * Centralized request handler with error handling and logging
     */
    private function makeRequest(string $method, string $endpoint, array $data = [], array $queryParams = []): ?array
    {
        try {
            $url = $this->baseUrl . $endpoint;
            
            if (!empty($queryParams)) {
                $url .= '?' . http_build_query($queryParams);
            }

            $request = Http::withHeaders($this->getHeaders());

            switch (strtoupper($method)) {
                case 'GET':
                    $response = $request->get($url);
                    break;
                case 'POST':
                    $response = $request->post($url, $data);
                    break;
                case 'PATCH':
                    $response = $request->patch($url, $data);
                    break;
                case 'DELETE':
                    $response = $request->delete($url);
                    break;
                default:
                    throw new \InvalidArgumentException("Unsupported HTTP method: {$method}");
            }

            if ($response->successful()) {
                $result = $response->json();
                return is_array($result) ? $result : null;
            }

            Log::warning('Accounting API: Request failed', [
                'method' => $method,
                'endpoint' => $endpoint,
                'data' => $data,
                'query_params' => $queryParams,
                'status_code' => $response->status(),
                'response' => $response->body(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Accounting API: Exception during request', [
                'method' => $method,
                'endpoint' => $endpoint,
                'data' => $data,
                'query_params' => $queryParams,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Get player information
     */
    public function getPlayerInfo(int $providerPlayerId): ?array
    {
        return $this->makeRequest('GET', "/player/{$providerPlayerId}/info");
    }

    /**
     * Get player devices
     */
    public function getPlayerDevices(int $providerPlayerId): ?array
    {
        return $this->makeRequest('GET', "/player/{$providerPlayerId}/devices", [], ['limit' => 999999]);
    }

    /**
     * Set player banned status
     */
    public function setPlayerBanned(int $providerPlayerId, bool $value): ?array
    {
        $action = $value ? 'ban' : 'unban';
        return $this->makeRequest('POST', "/player/{$providerPlayerId}/{$action}");
    }

    /**
     * Set device banned status
     */
    public function setDeviceBanned(int $deviceId, bool $value): ?array
    {
        $action = $value ? 'ban' : 'unban';
        return $this->makeRequest('POST', "/device/{$deviceId}/{$action}");
    }

    /**
     * Get devices report
     */
    public function getDevicesReport(string $lastLogin, ?array $deviceHashes = null, ?int $minConfirmedCount = null): ?array
    {
        $params = ['lastLogin' => $lastLogin];
        
        if ($deviceHashes) {
            $params['deviceHashes'] = $deviceHashes;
        }
        
        if ($minConfirmedCount) {
            $params['min'] = $minConfirmedCount;
        }

        return $this->makeRequest('GET', '/report/devices', [], $params);
    }

    /**
     * Get device players
     */
    public function getDevicePlayers(string $deviceHash, string $lastLogin): ?array
    {
        $params = [
            'deviceHash' => $deviceHash,
            'lastLogin' => $lastLogin,
        ];

        return $this->makeRequest('GET', '/report/device_players', [], $params);
    }

    /**
     * Set device quota
     */
    public function setDeviceQuota(int $deviceId, int $quota): ?array
    {
        return $this->makeRequest('POST', "/device/{$deviceId}/quota", ['quota' => $quota]);
    }

    /**
     * Get sum crypto by currency and transaction type
     */
    public function getSumCryptoByCurrencyAndTxType(\DateTime $from, \DateTime $to, string $currencyCode, string $txType): ?array
    {
        $params = [
            'fromDate' => $from->format('c'),
            'toDate' => $to->format('c'),
            'currency' => $currencyCode,
            'txType' => $txType,
        ];

        return $this->makeRequest('GET', '/report/sum_crypto_by_currency_and_tx_type', [], $params);
    }

    /**
     * Get players balance
     */
    public function getPlayersBalance(): ?array
    {
        return $this->makeRequest('GET', '/report/players_balance');
    }

    /**
     * Get crypto withdrawal fees
     */
    public function getCryptoWithdrawalFees(\DateTime $from, \DateTime $to): ?array
    {
        $params = [
            'fromDate' => $from->format('c'),
            'toDate' => $to->format('c'),
        ];

        return $this->makeRequest('GET', '/report/crypto_withdrawal_fees', [], $params);
    }

    /**
     * Get referrers
     */
    public function getReferrers(\DateTime $from, \DateTime $to, ?string $nickname = null): ?array
    {
        $params = [
            'fromDate' => $from->format('c'),
            'toDate' => $to->format('c'),
            'limit' => 1000000,
        ];

        if ($nickname) {
            $params['nickname'] = $nickname;
        }

        return $this->makeRequest('GET', '/referrers', [], $params);
    }

    /**
     * Get player referrals
     */
    public function getPlayerReferrals(int $referrerId): ?array
    {
        return $this->makeRequest('GET', "/player/{$referrerId}/referrals", [], ['limit' => 1000000]);
    }

    /**
     * Create player referral
     */
    public function createPlayerReferral(int $referrerId, int $referralId): ?array
    {
        return $this->makeRequest('POST', "/player/{$referrerId}/referrals/{$referralId}");
    }

    /**
     * Delete player referral
     */
    public function deletePlayerReferral(int $referrerId, int $referralId): ?array
    {
        return $this->makeRequest('DELETE', "/player/{$referrerId}/referrals/{$referralId}");
    }

    /**
     * Pay rakeback
     */
    public function payRakeback(): ?array
    {
        return $this->makeRequest('POST', '/rakeback/pay');
    }

    /**
     * Pay rakeback test
     */
    public function payRakebackTest(): ?array
    {
        return $this->makeRequest('POST', '/rakeback/pay_test');
    }

    /**
     * Get rakeback list
     */
    public function getRakebackList(\DateTime $fromDate, \DateTime $toDate, ?int $playerId = null): ?array
    {
        $params = [
            'fromDate' => $fromDate->format('Y-m-d'),
            'toDate' => $toDate->format('Y-m-d'),
        ];

        if ($playerId) {
            $params['playerId'] = $playerId;
        }

        return $this->makeRequest('GET', '/rakeback/list', [], $params);
    }

    /**
     * Get rakeback sum
     */
    public function getRakebackSum(\DateTime $from, \DateTime $to): ?array
    {
        $params = [
            'fromDate' => $from->format('c'),
            'toDate' => $to->format('c'),
        ];

        return $this->makeRequest('GET', '/report/rakeback', [], $params);
    }

    /**
     * Get CHP rake
     */
    public function getChpRake(\DateTime $from, \DateTime $to, ?int $playerId = null): ?array
    {
        $params = [
            'fromDate' => $from->format('c'),
            'toDate' => $to->format('c'),
        ];

        if ($playerId) {
            $params['playerId'] = $playerId;
        }

        return $this->makeRequest('GET', '/report/chp_rake', [], $params);
    }

    /**
     * Get CHP rake unique players
     */
    public function getChpRakeUniquePlayers(\DateTime $from, \DateTime $to): ?array
    {
        $params = [
            'fromDate' => $from->format('c'),
            'toDate' => $to->format('c'),
        ];

        return $this->makeRequest('GET', '/report/chp_rake_unique_players', [], $params);
    }

    /**
     * Get total rake or fee
     */
    public function getTotalRakeOrFee(\DateTime $from, \DateTime $to): ?array
    {
        static $cache = [];
        $key = $from->format('c') . $to->format('c');
        
        if (isset($cache[$key])) {
            return $cache[$key];
        }

        $params = [
            'fromDate' => $from->format('c'),
            'toDate' => $to->format('c'),
        ];

        $result = $this->makeRequest('GET', '/report/total_rake_or_fee', [], $params);
        return $cache[$key] = $result;
    }

    /**
     * Get conversions by players
     */
    public function getConversionsByPlayers(\DateTime $from, \DateTime $to): ?array
    {
        $params = [
            'fromDate' => $from->format('c'),
            'toDate' => $to->format('c'),
        ];

        return $this->makeRequest('GET', '/report/conversions_by_players', [], $params);
    }

    /**
     * Get rake conversion transactions
     */
    public function getRakeConversionTransactions(\DateTime $from, \DateTime $to): ?array
    {
        $params = [
            'fromDate' => $from->format('c'),
            'toDate' => $to->format('c'),
        ];

        return $this->makeRequest('GET', '/report/rake_conversion_transactions', [], $params);
    }

    /**
     * Get inactive player count
     */
    public function getInactivePlayerCount(int $days): int
    {
        // Note: Original implementation returns 0, keeping the same behavior
        return 0;
    }

    /**
     * Generate CIO stats
     */
    public function generateCioStats(): ?array
    {
        return $this->makeRequest('POST', '/cio/stats');
    }

    /**
     * Find players
     */
    public function findPlayers(array $params): ?array
    {
        $params['limit'] = 10000;
        return $this->makeRequest('GET', '/player/search', [], $params);
    }

    /**
     * Change email
     */
    public function changeEmail(int $playerId, string $newEmail): ?array
    {
        return $this->makeRequest('POST', "/player/{$playerId}/change_email/{$newEmail}");
    }

    /**
     * Get all deposit bonus configs
     */
    public function getAllDepositBonusConfigs(): ?array
    {
        return $this->makeRequest('GET', '/bonus/config/all');
    }

    /**
     * Create deposit bonus config
     */
    public function createDepositBonusConfig(array $data): ?array
    {
        return $this->makeRequest('POST', '/bonus/config', $data);
    }

    /**
     * Patch deposit bonus config
     */
    public function patchDepositBonusConfig(array $data): ?array
    {
        return $this->makeRequest('PATCH', '/bonus/config', $data);
    }

    /**
     * Get deposit bonus configs
     */
    public function getDepositBonusConfigs(?string $status = null): ?array
    {
        $params = [];
        if ($status) {
            $params['status'] = $status;
        }

        return $this->makeRequest('GET', '/bonus/configs', [], $params);
    }

    /**
     * Get deposit bonus config
     */
    public function getDepositBonusConfig(int $id): ?array
    {
        return $this->makeRequest('GET', "/bonus/config/{$id}");
    }

    /**
     * Give deposit bonus
     */
    public function giveDepositBonus(int $bonusConfigId, int $playerId): ?array
    {
        $data = [
            'bonus_config_id' => $bonusConfigId,
            'player_id' => $playerId,
        ];

        return $this->makeRequest('POST', '/bonus/give', $data);
    }

    /**
     * Get deposit bonuses
     */
    public function getDepositBonuses(array $filters, int $limit, int $page, ?string $order = null, ?string $sort = null): ?array
    {
        $data = [
            'from_date' => (new \DateTime($filters['from_date'], new \DateTimeZone('UTC')))->format('c'),
            'to_date' => (new \DateTime($filters['to_date'], new \DateTimeZone('UTC')))->format('c'),
            'limit' => $limit,
            'page' => $page,
        ];

        if ($order) {
            $data['order'] = $order;
        }
        if ($sort) {
            $data['sort'] = $sort;
        }

        if (isset($filters['state']) && $filters['state'] != 'All') {
            $data['state'] = $filters['state'];
        }

        if (isset($filters['nickname'])) {
            $data['nickname'] = $filters['nickname'];
        }

        if (isset($filters['bonus'])) {
            $data['bonus'] = $filters['bonus'];
        }

        return $this->makeRequest('GET', '/bonus', [], $data);
    }

    /**
     * Activate deposit bonus
     */
    public function activateDepositBonus(int $id): ?array
    {
        return $this->makeRequest('POST', "/bonus/activate/{$id}");
    }

    /**
     * Deactivate deposit bonus
     */
    public function deactivateDepositBonus(int $id): ?array
    {
        return $this->makeRequest('POST', "/bonus/deactivate/{$id}");
    }

    /**
     * Delete deposit bonus
     */
    public function deleteDepositBonus(int $id): ?array
    {
        return $this->makeRequest('DELETE', "/bonus/{$id}");
    }

    /**
     * Finish deposit bonus
     */
    public function finishDepositBonus(int $id): ?array
    {
        return $this->makeRequest('POST', "/bonus/finish/{$id}");
    }

    /**
     * Delete deposit bonus by player config
     */
    public function deleteDepositBonusByPlayerConfig(int $configId, int $playerProviderId): ?array
    {
        $data = [
            'bonus_config_id' => $configId,
            'player_id' => $playerProviderId,
        ];

        return $this->makeRequest('POST', '/bonus/delete', $data);
    }

    /**
     * Release deposit bonus
     */
    public function releaseDepositBonus(int $id, int $depositId, float $rakeAmount): ?array
    {
        $data = [
            'bonus_id' => $id,
            'deposit_id' => $depositId,
            'rake_amount' => $rakeAmount,
        ];

        return $this->makeRequest('POST', '/bonus/progress', $data);
    }

    /**
     * Get deposit bonus stats
     */
    public function getDepositBonusStats(array $params): ?array
    {
        $data = [
            'from_date' => (new \DateTime($params['from_date'], new \DateTimeZone('UTC')))->format('c'),
            'to_date' => (new \DateTime($params['to_date'], new \DateTimeZone('UTC')))->format('c'),
        ];

        return $this->makeRequest('GET', '/bonus/config/stats', [], $data);
    }

    /**
     * Get email verification token
     */
    public function getEmailVerificationToken(int $playerId): ?array
    {
        return $this->makeRequest('GET', "/player/{$playerId}/verification_token");
    }

    /**
     * Change affiliate ID
     */
    public function changeAffiliateId(int $playerId, string $newId): ?array
    {
        return $this->makeRequest('POST', "/player/{$playerId}/change_affiliate_id/{$newId}");
    }

    /**
     * Get features
     */
    public function getFeatures(): ?array
    {
        return $this->makeRequest('GET', '/player/features');
    }

    /**
     * Get player features
     */
    public function getPlayerFeatures(int $playerId): ?array
    {
        return $this->makeRequest('GET', "/player/{$playerId}/features");
    }

    /**
     * Set player feature
     */
    public function setPlayerFeature(int $playerId, int $featureId, bool $status): ?array
    {
        $data = [
            'feature_id' => $featureId,
            'status' => $status,
        ];

        return $this->makeRequest('POST', "/player/{$playerId}/feature", $data);
    }

    /**
     * Delete player feature
     */
    public function deletePlayerFeature(int $playerId, int $featureId): ?array
    {
        return $this->makeRequest('DELETE', "/player/{$playerId}/feature/{$featureId}");
    }

    /**
     * Check if the API is available
     */
    public function isAvailable(): bool
    {
        return !empty($this->baseUrl) && !empty($this->apiToken);
    }
} 