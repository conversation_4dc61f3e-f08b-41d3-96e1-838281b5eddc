<?php

namespace App\BackOffice\CoinPoker\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CoinPokerApiService
{
    protected string $baseUrl;
    protected string $apiToken;

    public function __construct()
    {
        $this->baseUrl = config('services.coinpoker.coinpoker_api.api_url');
        $this->apiToken = config('services.coinpoker.coinpoker_api.api_token');
    }

    /**
     * Get common HTTP headers for API requests
     */
    private function getHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiToken,
            'Content-Type' => 'application/json',
        ];
    }

    /**
     * Find players by wallet address
     */
    public function findPlayerByWalletAddress(string $walletAddress): ?array
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->get($this->baseUrl . '/player/findByWalletAddress', [
                    'address' => $walletAddress,
                    'platformId' => 1, // ETHEREUM platform ID
                ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data;
            }

            Log::warning('CoinPoker API: Failed to find player by wallet address', [
                'wallet_address' => $walletAddress,
                'response' => $response->body(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('CoinPoker API: Exception finding player by wallet address', [
                'wallet_address' => $walletAddress,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Find players by various criteria (nickname, email, phone number)
     */
    public function findPlayers(array $criteria): array
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->post($this->baseUrl . '/player/find', $criteria);

            if ($response->successful()) {
                return $response->json() ?? [];
            }

            Log::warning('CoinPoker API: Failed to find players', [
                'criteria' => $criteria,
                'response' => $response->body(),
            ]);

            return [];
        } catch (\Exception $e) {
            Log::error('CoinPoker API: Exception finding players', [
                'criteria' => $criteria,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Find a single player by provider ID (player_provider_id)
     */
    public function findPlayerByProviderId(int $providerId): ?array
    {
        $url = $this->baseUrl . '/admin/player/' . $providerId .'/info';
        try {

            $response = Http::withHeaders($this->getHeaders())
                ->get($url);
            if ($response->successful()) {
                $data = $response->json();

                // Transform the API response to match our expected format
                return $this->transformApiResponse($data);
            }

            Log::warning('CoinPoker API: Failed to find player by provider ID', [
                'provider_id' => $providerId,
                'response' => $response->body(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('CoinPoker API: Exception finding player by provider ID', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Transform the API response to match our expected format
     */
    private function transformApiResponse(array $apiData): array
    {
        $profile = $apiData['profile'] ?? [];
        $identity = $apiData['identity'] ?? [];
        $locations = $apiData['locations'] ?? [];
        $wallets = $apiData['wallets'] ?? [];

        // Extract phone number from identity
        $phoneNumber = null;
        $phoneConfirmed = false;
        foreach ($identity as $identityItem) {
            if (($identityItem['name'] ?? '') === 'Phone') {
                $phoneNumber = $identityItem['value'] ?? null;
                $phoneConfirmed = $identityItem['confirmed'] ?? false;
                break;
            }
        }

        // Extract email from identity
        $email = null;
        $emailConfirmed = false;
        foreach ($identity as $identityItem) {
            if (($identityItem['name'] ?? '') === 'Email') {
                $email = $identityItem['value'] ?? null;
                $emailConfirmed = $identityItem['confirmed'] ?? false;
                break;
            }
        }

        // Get the most recent location
        $latestLocation = null;
        if (!empty($locations)) {
            $latestLocation = collect($locations)
                ->sortByDesc('confirmed_at')
                ->first();
        }

        // Get wallet addresses
        $walletAddresses = collect($wallets)
            ->pluck('address')
            ->filter()
            ->toArray();

        return [
            'id' => $profile['id'] ?? null,
            'email' => $email ?? $profile['email'] ?? null,
            'nickname' => $profile['nickname'] ?? null,
            'phone_number' => $phoneNumber,
            'phone_confirmation_info' => $phoneConfirmed ? 'Confirmed' : ($phoneNumber ? 'Not Confirmed' : 'Not Set'),
            'email_confirmation_info' => $emailConfirmed ? 'Confirmed' : ($email ? 'Not Confirmed' : 'Not Set'),
            'balance_usdt' => $profile['balance'] ?? null,
            'balance_chp' => $profile['balance_chp'] ?? null,
            'banned' => (bool)($profile['banned'] ?? false),
            'affiliate' => $profile['affiliate'] ?? null,
            'affiliate_id' => $profile['affiliate_id'] ?? null,
            'avatar' => $profile['avatar'] ?? null,
            'created_at' => $profile['created_at'] ?? null,
            'referrer_id' => $profile['referrer_id'] ?? null,
            'offline_code' => $profile['offline_code'] ?? null,
            'latest_ip' => $latestLocation['ip'] ?? null,
            'latest_country' => $latestLocation['country'] ?? null,
            'latest_location_confirmed_at' => $latestLocation['confirmed_at'] ?? null,
            'wallet_addresses' => $walletAddresses,
            'total_locations' => count($locations),
            'total_wallets' => count($wallets),
        ];
    }

    /**
     * Find a single player by ID (legacy method - kept for backward compatibility)
     * @deprecated Use findPlayerByProviderId instead
     */
    public function findPlayerById(int $playerId): ?array
    {
        return $this->findPlayerByProviderId($playerId);
    }

    /**
     * Check if the API is available
     */
    public function isAvailable(): bool
    {
        return !empty($this->baseUrl) && !empty($this->apiToken);
    }

    /**
     * Get player devices from API
     */
    public function getPlayerDevices(int $providerId): ?array
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->get($this->baseUrl . '/admin/player/' . $providerId . '/devices');

            if ($response->successful()) {
                $data = $response->json();
                return $data['data'] ?? [];
            }

            Log::warning('CoinPoker API: Failed to get player devices', [
                'provider_id' => $providerId,
                'response' => $response->body(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('CoinPoker API: Exception getting player devices', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get player sessions from API
     */
    public function getPlayerSessions(int $providerId, string $fromDate = null, string $toDate = null): ?array
    {
        try {
            // Default to last 7 days if no dates provided
            if (!$fromDate) {
                $fromDate = date('Y-m-d', strtotime('-7 days'));
            }
            if (!$toDate) {
                $toDate = date('Y-m-d');
            }

            $params = [
                'fromDate' => $fromDate . ' 00:00:00',
                'toDate' => $toDate . ' 23:59:59',
                'limit' => 99999
            ];

            $response = Http::withHeaders($this->getHeaders())
                ->get($this->baseUrl . '/admin/player/' . $providerId . '/sessions', $params);

            if ($response->successful()) {
                $data = $response->json();
                return $data['data'] ?? [];
            }

            Log::warning('CoinPoker API: Failed to get player sessions', [
                'provider_id' => $providerId,
                'response' => $response->body(),
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('CoinPoker API: Exception getting player sessions', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Force logout a player
     */
    public function forceLogoutPlayer(int $providerId): bool
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->post($this->baseUrl . '/admin/player/' . $providerId . '/force-logout');

            if ($response->successful()) {
                return true;
            }

            Log::warning('CoinPoker API: Failed to force logout player', [
                'provider_id' => $providerId,
                'response' => $response->body(),
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('CoinPoker API: Exception force logging out player', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Ban or unban a player
     */
    public function togglePlayerBan(int $providerId, bool $banned): bool
    {
        try {
            $action = $banned ? 'ban' : 'unban';
            $response = Http::withHeaders($this->getHeaders())
                ->post($this->baseUrl . '/admin/player/' . $providerId . '/' . $action);

            if ($response->successful()) {
                return true;
            }

            Log::warning('CoinPoker API: Failed to ' . $action . ' player', [
                'provider_id' => $providerId,
                'response' => $response->body(),
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('CoinPoker API: Exception ' . $action . 'ing player', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Change player email
     */
    public function changePlayerEmail(int $providerId, string $newEmail): bool
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->post($this->baseUrl . '/admin/player/' . $providerId . '/change-email', [
                    'email' => $newEmail
                ]);

            if ($response->successful()) {
                return true;
            }

            Log::warning('CoinPoker API: Failed to change player email', [
                'provider_id' => $providerId,
                'response' => $response->body(),
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('CoinPoker API: Exception changing player email', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Change player affiliate ID
     */
    public function changePlayerAffiliateId(int $providerId, string $newAffiliateId): bool
    {
        try {
            $response = Http::withHeaders($this->getHeaders())
                ->post($this->baseUrl . '/admin/player/' . $providerId . '/change-affiliate-id', [
                    'affiliate_id' => $newAffiliateId
                ]);

            if ($response->successful()) {
                return true;
            }

            Log::warning('CoinPoker API: Failed to change player affiliate ID', [
                'provider_id' => $providerId,
                'response' => $response->body(),
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('CoinPoker API: Exception changing player affiliate ID', [
                'provider_id' => $providerId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Magic method to intercept instance method calls
     */
    public function __call(string $method, array $arguments)
    {
        Log::info('CoinPokerApiService: Intercepted instance method call', [
            'method' => $method,
            'arguments' => $arguments,
            'class' => static::class,
        ]);

        // Check if the method exists and call it
        if (method_exists($this, $method)) {
            return $this->$method(...$arguments);
        }

        // You can add custom logic here for handling undefined methods
        // For example, you could implement dynamic API endpoint calls

        throw new \BadMethodCallException("Method {$method} does not exist on " . static::class);
    }

    /**
     * Magic method to intercept static method calls
     */
    public static function __callStatic(string $method, array $arguments)
    {
        Log::info('CoinPokerApiService: Intercepted static method call', [
            'method' => $method,
            'arguments' => $arguments,
            'class' => static::class,
        ]);

        // First check if the method exists as a static method
        if (method_exists(static::class, $method) && (new \ReflectionMethod(static::class, $method))->isStatic()) {
            return static::$method(...$arguments);
        }

        // If not static, create a new instance and call the method if it exists
        $instance = new static();
        if (method_exists($instance, $method)) {
            return $instance->$method(...$arguments);
        }

        // You can add custom logic here for handling undefined static methods
        // For example, you could create a new instance and call the method

        throw new \BadMethodCallException("Static method {$method} does not exist on " . static::class);
    }
}
