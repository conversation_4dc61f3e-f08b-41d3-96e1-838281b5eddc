<?php

namespace App\BackOffice\CoinPoker\Services;

use App\BackOffice\CoinPoker\Contracts\PlayerWriteDataSourceInterface;

class PlayerService
{
    protected PlayerWriteDataSourceInterface $writeDataSource;

    public function __construct(PlayerWriteDataSourceInterface $writeDataSource)
    {
        $this->writeDataSource = $writeDataSource;
    }

    public function updateRestrictionGroup(string|int $groupId, array $data): bool
    {
        return $this->writeDataSource->updateRestrictionGroup($groupId, $data);
    }

    public function createRestrictionGroup(array $data): bool
    {
        return $this->writeDataSource->createRestrictionGroup($data);
    }

    public function deleteRestrictionGroups(array $groupIds): bool
    {
        return $this->writeDataSource->deleteRestrictionGroups($groupIds);
    }

    public function addCaptcha(int $playerId): bool
    {
        return $this->writeDataSource->addCaptcha($playerId);
    }

    public function banPlayersByIp(int $ip): array
    {
        return $this->writeDataSource->banPlayersByIp($ip);
    }

    public function updatePlayerBanStatus(int $playerId, bool $shouldBan): bool
    {
        return $this->writeDataSource->updatePlayerBanStatus($playerId, $shouldBan);
    }

    public function setConstantScreenshoting(int $playerId, bool $enable): bool
    {
        return $this->writeDataSource->setConstantScreenshoting($playerId, $enable);
    }
}
