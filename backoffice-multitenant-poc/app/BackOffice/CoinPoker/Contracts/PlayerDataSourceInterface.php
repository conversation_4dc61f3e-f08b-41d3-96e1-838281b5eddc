<?php

namespace App\BackOffice\CoinPoker\Contracts;

use Illuminate\Pagination\LengthAwarePaginator;

interface PlayerDataSourceInterface
{
    /**
     * Get players with filters, result set options, and sorting
     *
     * @param  array  $filters  - filters and search criteria
     * @param  array  $resultSetOptions  - result set options with 'page', 'perPage', 'skip', 'limit' keys
     * @param  array  $sorting  - sorting criteria
     * @return mixed - LengthAwarePaginator if page/perPage provided, Collection with skip/limit otherwise
     */
    public function getPlayers(array $filters, array $resultSetOptions, array $sorting = []);

    /**
     * Get players for provider table with pagination, filters, and sorting
     */
    public function getPlayersForProviderTable(array $filters, int $page, int $perPage, array $sorting = [], ?string $search = null);

    /**
     * Get a single player by ID
     */
    public function getAccountingApiPlayerInfo(string $playerId): ?array;

    /**
     * Get poker game sessions for the Poker Game Check page with filters, pagination, and sorting
     */
    public function getPokerGameSessions(array $filters, int $page, int $perPage, array $sorting = [], ?string $search = null);

    /**
     * Get restriction groups with pagination, filters, and sorting
     */
    public function getRestrictionGroups(array $filters, int $page, int $perPage, array $sorting = []): LengthAwarePaginator;

    /**
     * Get a single restriction group by ID
     */
    public function getRestrictionGroupById(int|string $id): ?array;

    /**
     * Get players with constant screenshoting enabled for tracking
     */
    public function getPlayersTracking(array $filters, array $resultSetOptions, array $sorting = []);

    /**
     * Get players captcha tracking data
     */
    public function getPlayersCaptchaTracking(array $filters, array $resultSetOptions, array $sorting = []);

    /**
     * Get players heatmap data for anti-cheat analysis
     */
    public function getPlayersHeatmap(array $filters, array $resultSetOptions, array $sorting = []);

    /**
     * Get multi-account check data (devices or IP-based tracking)
     */
    public function getMultiAccountData(array $filters, int $page, int $recordsPerPage): LengthAwarePaginator;

    /**
     * Get detailed heatmap records for a specific player
     */
    public function getPlayerHeatmapDetails(int $playerId, array $filters, array $resultSetOptions, array $sorting = []): LengthAwarePaginator;

    /**
     * Get a single player by player ID or criteria array
     */
    public function getPlayer(int|array $criteria): ?array;
}
