<?php

namespace App\BackOffice\CoinPoker\Contracts;

interface PlayerWriteDataSourceInterface
{
    public function updateRestrictionGroup(string|int $groupId, array $data): bool;

    public function createRestrictionGroup(array $data): bool;

    public function deleteRestrictionGroups(array $groupIds): bool;

    public function addCaptcha(int $playerId): bool;

    public function banPlayersByIp(int $ip): array;

    public function updatePlayerBanStatus(int $playerId, bool $shouldBan): bool;

    public function setConstantScreenshoting(int $playerId, bool $enable): bool;
}
