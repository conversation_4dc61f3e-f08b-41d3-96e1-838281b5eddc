<?php

namespace App\BackOffice\CoinPoker\Widgets;

use Filament\Widgets\Widget;

class PlayerInfoWidget extends Widget
{
    protected string $view = 'filament.widgets.player-info-widget';

    protected static bool $isDiscovered = false;

    public ?array $playerData = null;

    protected int|string|array $columnSpan = 'full';

    public function mount(?array $playerData = null): void
    {
        $this->playerData = $playerData;
    }
}
