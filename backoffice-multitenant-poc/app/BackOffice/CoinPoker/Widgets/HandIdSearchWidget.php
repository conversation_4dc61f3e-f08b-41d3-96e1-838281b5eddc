<?php

namespace App\BackOffice\CoinPoker\Widgets;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Schemas\Components\Grid;
use Filament\Widgets\Widget;
use Illuminate\Contracts\View\View;

class HandIdSearchWidget extends Widget implements HasForms
{
    use InteractsWithForms;

    protected string $view = 'CoinPoker::widgets.hand-id-search-widget';

    protected static bool $isDiscovered = false;

    public ?array $data = [];

    public ?string $handId = null;

    public function render(): View
    {
        return view($this->view, [
            'form' => $this->form,
        ]);
    }

    public function getFormSchema(): array
    {
        return [
            Grid::make(6)
                ->schema([
                    TextInput::make('handId')
                        ->label('Hand ID')
                        ->extraAttributes(['class' => 'my-2'])
                        ->columnSpan(2)
                        ->placeholder('Enter Hand ID')
                        ->required()
                        ->maxLength(255),
                ]),
        ];
    }

    public function searchHand(): void
    {
        $handId = $this->validate(['handId' => 'required|string|max:255'])['handId'];

        if (! empty($handId)) {
            redirect()->to(
                route('filament.admin.pages.hand-details', compact('handId'))
            );
        }
    }
}
