<?php

namespace App\BackOffice\CoinPoker\Widgets;

use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class PlayerIdentificationsListWidget extends BaseWidget
{
    protected static ?int $sort = 2;

    protected static bool $isDiscovered = false;

    public ?array $tableFilters = [];

    public array $identifications = [];

    protected int|string|array $columnSpan = 'full';

    protected function getTableColumns(): array
    {
        return [
            TextColumn::make('value')->label('Email/Phone'),
            TextColumn::make('confirmed')->label('Confirmed')->formatStateUsing(fn ($state) => $state ? 'Yes' : 'No'),
            TextColumn::make('next_reconfirm_at')->label('Next Reconfirm At'),
            TextColumn::make('actions')
                ->label('Actions')
                ->formatStateUsing(fn ($record) => view('filament.widgets.player-identifications-actions', ['identification' => $record])),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns($this->getTableColumns())
            ->records(fn () => $this->identifications);
    }
}
