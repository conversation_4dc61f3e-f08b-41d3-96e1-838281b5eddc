<?php

namespace App\BackOffice\CoinPoker\Widgets;

use App\BackOffice\CoinPoker\Repositories\PlayerRepository;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class ProviderPlayersWidget extends BaseWidget
{
    protected static ?int $sort = 2;

    protected static bool $isDiscovered = false;

    public ?array $tableFilters = [];

    protected int|string|array $columnSpan = 'full';

    protected $listeners = ['player-filters-updated' => 'handleTableFilterUpdates'];

    protected function getTableColumns(): array
    {
        return [
            TextColumn::make('provider_player_id')->label('Provider Player ID'),
            TextColumn::make('email')->label('Email'),
            TextColumn::make('nickname')->label('Nickname'),
            TextColumn::make('phone_number')->label('Phone Number'),
            TextColumn::make('phone_confirmation_info')->label('Phone Confirmation Info'),
            TextColumn::make('actions')
                ->label('Actions')
                ->formatStateUsing(fn ($record) => view('filament.widgets.provider-players-actions', ['player' => $record])),
        ];
    }

    public function handleTableFilterUpdates($filters = null): void
    {
        if ($filters !== null) {
            $this->tableFilters = $filters;
        }
        $this->dispatch('$refresh');
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns($this->getTableColumns())
            ->records(function (
                ?string $sortColumn,
                ?string $sortDirection,
                ?string $search,
                int $page,
                int $recordsPerPage
            ) {
                return app(PlayerRepository::class)->getPlayersForProviderTable(
                    filters: $this->tableFilters ?? [],
                    page: $page,
                    perPage: $recordsPerPage,
                    sorting: [
                        'column' => $sortColumn,
                        'direction' => $sortDirection,
                    ],
                    search: $search
                );
            });
    }
}
