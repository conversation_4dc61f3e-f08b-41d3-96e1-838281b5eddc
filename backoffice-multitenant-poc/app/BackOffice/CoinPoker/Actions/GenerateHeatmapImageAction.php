<?php

namespace App\BackOffice\CoinPoker\Actions;

use App\BackOffice\CoinPoker\Models\GamingSystem\AntiCheatHeatmap;
use App\BackOffice\CoinPoker\Services\BlobProcessorService;
use App\BackOffice\CoinPoker\Services\HeatmapBackgroundService;
use Illuminate\Http\Response;

class GenerateHeatmapImageAction
{
    private BlobProcessorService $blobProcessor;

    private HeatmapBackgroundService $backgroundService;

    public function __construct(
        BlobProcessorService $blobProcessor,
        HeatmapBackgroundService $backgroundService
    ) {
        $this->blobProcessor = $blobProcessor;
        $this->backgroundService = $backgroundService;
    }

    public function execute(int $antiCheatHeatmapId): Response
    {
        // @todo: Avoid using the model, use a PlayerRepository instead
        $heatmap = AntiCheatHeatmap::with('playerAccount')->findOrFail($antiCheatHeatmapId);

        if (! $heatmap->hasValidBlobData()) {
            return $this->generatePlaceholderImage($heatmap, 'No heatmap data available');
        }

        try {
            $backgroundImage = $this->backgroundService->getBackgroundImage($heatmap);
            $backgroundPath = $this->backgroundService->getBackgroundPath($backgroundImage);

            if (! file_exists($backgroundPath)) {
                throw new \Exception("Background image not found: {$backgroundImage}");
            }

            $blobData = $heatmap->getBlobData();
            $imageContent = $this->blobProcessor->generateHeatmapImage($blobData, $backgroundPath);

            return response($imageContent, 200, [
                'Content-Type' => 'image/png',
                'Content-Disposition' => 'inline; filename="heatmap_'.$antiCheatHeatmapId.'.png"',
                'Cache-Control' => 'public, max-age=3600',
            ]);
        } catch (\Exception $exception) {
            report($exception);

            return $this->generatePlaceholderImage($heatmap, $exception->getMessage());
        }
    }

    // @todo: Don't use model typehint, use a PlayerRepository instead
    protected function generatePlaceholderImage(AntiCheatHeatmap $heatmap, string $errorMessage = 'Image generation unavailable'): Response
    {
        $width = 800;
        $height = 600;
        $image = imagecreate($width, $height);

        $backgroundColor = imagecolorallocate($image, 240, 240, 240);
        $textColor = imagecolorallocate($image, 100, 100, 100);
        $borderColor = imagecolorallocate($image, 200, 200, 200);

        imagefill($image, 0, 0, $backgroundColor);
        imagerectangle($image, 0, 0, $width - 1, $height - 1, $borderColor);

        $text1 = "Heatmap #{$heatmap->anti_cheat_heatmap_id}";
        $text2 = "Player: {$heatmap->playerAccount?->nick_name}";
        $text3 = "Game: {$heatmap->getGameTypeDisplay()} ({$heatmap->getGameEventTypeDisplay()})";
        $text4 = "Evaluation: {$heatmap->eval_result}";
        $text5 = $errorMessage;

        imagestring($image, 4, ($width - strlen($text1) * 10) / 2, $height / 2 - 50, $text1, $textColor);
        imagestring($image, 3, ($width - strlen($text2) * 8) / 2, $height / 2 - 20, $text2, $textColor);
        imagestring($image, 3, ($width - strlen($text3) * 8) / 2, $height / 2, $text3, $textColor);
        imagestring($image, 3, ($width - strlen($text4) * 8) / 2, $height / 2 + 20, $text4, $textColor);
        imagestring($image, 2, ($width - strlen($text5) * 6) / 2, $height / 2 + 50, $text5, $textColor);

        ob_start();
        imagepng($image);
        $imageContent = ob_get_contents();
        ob_end_clean();

        imagedestroy($image);

        return response($imageContent, 200, [
            'Content-Type' => 'image/png',
            'Content-Disposition' => 'inline; filename="heatmap_placeholder_'.$heatmap->anti_cheat_heatmap_id.'.png"',
        ]);
    }
}
