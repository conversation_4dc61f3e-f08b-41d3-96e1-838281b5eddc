<?php

namespace App\BackOffice\CoinPoker\Providers;

use App\BackOffice\CoinPoker\Contracts\PlayerDataSourceInterface;
use App\BackOffice\CoinPoker\Contracts\PlayerWriteDataSourceInterface;
use App\BackOffice\CoinPoker\DataSources\ApiPlayerDataSource;
use App\BackOffice\CoinPoker\DataSources\ApiPlayerWriteDataSource;
use App\BackOffice\CoinPoker\DataSources\LegacyPlayerDataSource;
use App\BackOffice\CoinPoker\DataSources\LegacyPlayerWriteDataSource;
use App\BackOffice\CoinPoker\Models\SushiPlayerAccount;
use App\BackOffice\CoinPoker\Models\SushiRestrictionGroup;
use App\BackOffice\CoinPoker\Repositories\PlayerRepository;
use App\BackOffice\CoinPoker\Resources\PlayerResource\Pages\HandExport;
use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages\CaptchaList;
use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages\Heatmap;
use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages\MultiAccountCheck;
use App\BackOffice\CoinPoker\Resources\RestrictionGroups\Pages\ListRestrictionGroups;
use App\BackOffice\CoinPoker\Services\AccountingApiService;
use App\BackOffice\CoinPoker\Services\PlayerService;
use Filament\Facades\Filament;
use Filament\Navigation\NavigationItem;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;

class CoinPokerServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(PlayerDataSourceInterface::class, function ($app) {
            $strategy = config('coinpoker.players.strategy');

            return match ($strategy) {
                'legacy' => $app->make(LegacyPlayerDataSource::class),
                'api' => $app->make(ApiPlayerDataSource::class),
                default => $app->make(LegacyPlayerDataSource::class),
            };
        });

        $this->app->bind(PlayerWriteDataSourceInterface::class, function ($app) {
            $strategy = config('coinpoker.players.strategy');

            return match ($strategy) {
                'legacy' => $app->make(LegacyPlayerWriteDataSource::class),
                'api' => $app->make(ApiPlayerWriteDataSource::class),
                default => $app->make(LegacyPlayerWriteDataSource::class),
            };
        });

        $this->app->singleton(PlayerRepository::class);
        $this->app->singleton(PlayerService::class);
        $this->app->singleton(AccountingApiService::class);
    }

    public function boot()
    {
        Route::bind('restrictionGroup', function ($id) {
            $rowData = app(PlayerRepository::class)->getRestrictionGroupById($id);
            if (empty($rowData)) {
                abort(404);
            }

            return SushiRestrictionGroup::addRow($rowData)->where('id', $id)->first();
        });

        Route::bind('playerAccount', function ($id) {
            $rowData = app(PlayerRepository::class)->getPlayer($id);
            if (empty($rowData)) {
                abort(404);
            }

            return SushiPlayerAccount::addRow($rowData)->where('player_id', $id)->first();
        });

        $this->loadViewsFrom(resource_path('views/CoinPoker'), 'CoinPoker');

        Filament::serving(function () {
            Filament::registerNavigationItems([
                // Players
                NavigationItem::make('Players List')
                    ->url(route('filament.admin.resources.players.index'))
                    ->sort(2)
                    ->icon('heroicon-o-users')
                    ->group('Players'),
                NavigationItem::make('Poker Game Check')
                    ->url(route('filament.admin.resources.players.poker-game-check'))
                    ->sort(2)
                    ->icon('heroicon-o-magnifying-glass')
                    ->group('Players'),
                NavigationItem::make('Hand Export')
                    ->url(HandExport::getUrl())
                    ->sort(3)
                    ->icon('heroicon-o-document-arrow-down')
                    ->group('Players'),
                NavigationItem::make('Restriction Groups')
                    ->url(ListRestrictionGroups::getUrl())
                    ->sort(4)
                    ->icon('heroicon-o-rectangle-stack')
                    ->group('Players'),

                // Players Tracking
                NavigationItem::make('Captcha List')
                    ->url(CaptchaList::getUrl())
                    ->sort(1)
                    ->icon('heroicon-o-shield-check')
                    ->group('Players Tracking'),
                NavigationItem::make('Multi Account Check')
                    ->url(MultiAccountCheck::getUrl())
                    ->sort(2)
                    ->icon('heroicon-o-user-group')
                    ->group('Players Tracking'),
                NavigationItem::make('Heatmap')
                    ->url(Heatmap::getUrl())
                    ->sort(4)
                    ->icon('heroicon-o-map')
                    ->group('Players Tracking'),
            ]);
        });
    }
}
