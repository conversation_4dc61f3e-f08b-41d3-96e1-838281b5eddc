<?php

namespace App\BackOffice\CoinPoker\Resources\PlayerResource\Pages;

use App\BackOffice\CoinPoker\DataSources\LegacyPlayerDataSource;
use App\BackOffice\CoinPoker\Resources\PlayerResource;
use App\BackOffice\Shared\Services\TenantPermissionService;
use Filament\Resources\Pages\ViewRecord;

class ViewPlayer extends ViewRecord
{
    protected static string $resource = PlayerResource::class;

    protected ?array $playerData = null;

    protected array $identifications = [];

    public function mount(int|string $record): void
    {
        if (! TenantPermissionService::canRead()) {
            abort(403, 'You do not have permission to view players for this tenant.');
        }

        $dataSource = app(LegacyPlayerDataSource::class);
        $data = $dataSource->getAccountingApiPlayerInfo($record);
        $this->playerData = $data;
        $this->identifications = $this->parseIdentifications();
        // @todo: Load these table datas from the proper source
        //        $this->devices = [];
        //        $this->playerSessions = [];

        parent::mount($record);
    }

    public function getTitle(): string
    {
        $tenantName = TenantPermissionService::getTenantName();

        return "View Player ({$tenantName})";
    }

    protected function parseIdentifications(): array
    {
        $parsedIdentifications = [];
        foreach ($this->playerData['identity'] ?? [] as $i) {
            $parsedIdentifications[] = [
                'name' => $i['name'] ?? '',
                'value' => $i['value'] ?? '-',
                'confirmed' => $i['confirmed'] ?? false,
                'next_reconfirm_at' => ! empty($i['reconfirm_at']) ? (new \DateTime($i['reconfirm_at']))->format('Y-m-d H:i:s') : '-',
            ];
        }

        return $parsedIdentifications;
    }

    protected function getFooterWidgets(): array
    {
        return [
            \App\BackOffice\CoinPoker\Widgets\PlayerIdentificationsListWidget::make(['identifications' => $this->identifications]),
            // @todo: bring DevicesWidget
            // @todo: bring PlayerSessionsWidget
        ];
    }
}
