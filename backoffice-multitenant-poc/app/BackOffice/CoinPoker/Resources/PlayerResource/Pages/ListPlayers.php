<?php

namespace App\BackOffice\CoinPoker\Resources\PlayerResource\Pages;

use App\BackOffice\CoinPoker\Resources\PlayerResource;
use App\BackOffice\CoinPoker\Widgets\ProviderPlayersWidget;
use App\BackOffice\Shared\CustomListRecords;
use Filament\Actions\Action;
use Filament\Resources\Pages\ListRecords;

class ListPlayers extends ListRecords
{
    use CustomListRecords;

    protected static string $resource = PlayerResource::class;

    protected static ?string $title = 'Players List';

    protected function getHeaderActions(): array
    {
        return [
            Action::make('poker_game_check')
                ->label('Poker Game Check')
                ->icon('heroicon-o-magnifying-glass')
                ->color('primary')
                ->url(route('filament.admin.resources.players.poker-game-check'))
                ->openUrlInNewTab(),
            Action::make('poker_hands')
                ->label('Hand Export')
                ->icon('heroicon-o-play')
                ->color('primary')
                ->url(route('filament.admin.resources.players.hand-export'))
                ->openUrlInNewTab(),
        ];
    }

    protected function handleTableFilterUpdates(): void
    {
        $this->dispatch('player-filters-updated', $this->getTableFiltersForWidget());
        parent::handleTableFilterUpdates();
    }

    protected function getFooterWidgets(): array
    {
        return [
            ProviderPlayersWidget::make(['tableFilters' => $this->getTableFiltersForWidget()]),
        ];
    }

    private function getTableFiltersForWidget(): array
    {
        $filters = [];
        $filterNames = ['nick_name', 'email', 'phone_number', 'player_provider_id'];

        foreach ($filterNames as $filterName) {
            // @todo: review this basic_filters
            $filterState = $this->getTableFilterState('basic_filters');
            if (! empty($filterState[$filterName])) {
                $filters[$filterName] = $filterState[$filterName];
            }
        }

        return $filters;
    }
}
