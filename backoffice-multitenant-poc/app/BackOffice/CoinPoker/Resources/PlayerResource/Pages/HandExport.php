<?php

namespace App\BackOffice\CoinPoker\Resources\PlayerResource\Pages;

use App\BackOffice\CoinPoker\Resources\PlayerResource;
use App\BackOffice\CoinPoker\Services\HandLogService;
use Exception;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Filament\Schemas\Components\Grid;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Livewire\Attributes\Url;

class HandExport extends Page implements HasForms
{
    use InteractsWithForms;

    public const string SEARCH_BY_PLAYER_ID = '1';

    public const string SEARCH_BY_HAND_ID = '2';

    public const string SEARCH_BY_TOURNAMENT_ID = '3';

    public const string SEARCH_BY_TABLE_ID = '4';

    public const string SEARCH_BY_SESSION_HASH = '5';

    public const array SEARCH_BY_OPTIONS = [
        self::SEARCH_BY_PLAYER_ID => 'Player ID',
        self::SEARCH_BY_HAND_ID => 'Hand ID',
        self::SEARCH_BY_TOURNAMENT_ID => 'Tournament ID',
        self::SEARCH_BY_TABLE_ID => 'Table ID',
        self::SEARCH_BY_SESSION_HASH => 'Session Hash',
    ];

    protected static string $resource = PlayerResource::class;

    protected static ?string $title = 'Hand Export';

    protected static ?string $navigationLabel = 'Hand Export';

    protected static ?string $slug = 'hand-export';

    protected string $view = 'filament.resources.player-resource.pages.hand-export';

    #[Url]
    public ?array $data = [];

    public function mount(): void
    {
        if (empty($this->data)) {
            $this->form->fill([
                'search_by' => self::SEARCH_BY_PLAYER_ID,
                'from' => null,
                'to' => null,
                'with_player_hole_cards' => true,
                'with_server_info' => false,
            ]);
        } else {
            $this->form->fill(array_merge($this->data, [
                'with_player_hole_cards' => filter_var($this->data['with_player_hole_cards'] ?? false, FILTER_VALIDATE_BOOLEAN),
                'with_server_info' => filter_var($this->data['with_server_info'] ?? false, FILTER_VALIDATE_BOOLEAN),
            ]));
        }
    }

    public function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Section::make()
                    ->schema([
                        Select::make('search_by')
                            ->label('Search by')
                            ->options(self::SEARCH_BY_OPTIONS)
                            ->required()
                            ->reactive()
                            ->maxWidth('sm'),
                        Grid::make(2)->schema([
                            DateTimePicker::make('from')
                                ->nullable()
                                ->required(fn ($get) => $get('search_by') === self::SEARCH_BY_PLAYER_ID)
                                ->maxWidth('sm'),
                            DateTimePicker::make('to')
                                ->nullable()
                                ->required(fn ($get) => $get('search_by') === self::SEARCH_BY_PLAYER_ID)
                                ->maxWidth('sm'),
                        ])->columns(5)
                            ->visible(fn ($get) => $get('search_by') === self::SEARCH_BY_PLAYER_ID)->columnSpanFull(),

                        TextInput::make('player_id')
                            ->label('Player ID')
                            ->required(fn ($get) => $get('search_by') === self::SEARCH_BY_PLAYER_ID)
                            ->visible(fn ($get) => $get('search_by') === self::SEARCH_BY_PLAYER_ID)
                            ->autocomplete('hand-export-player-id')
                            ->maxWidth('sm'),

                        TextInput::make('hand_id')
                            ->label('Hand ID')
                            ->required(fn ($get) => $get('search_by') === self::SEARCH_BY_HAND_ID)
                            ->visible(fn ($get) => $get('search_by') === self::SEARCH_BY_HAND_ID)
                            ->autocomplete('hand-export-hand-id')
                            ->maxWidth('sm'),

                        TextInput::make('tournament_id')
                            ->label('Tour. ID')
                            ->required(fn ($get) => $get('search_by') === self::SEARCH_BY_TOURNAMENT_ID)
                            ->visible(fn ($get) => $get('search_by') === self::SEARCH_BY_TOURNAMENT_ID)
                            ->autocomplete('hand-export-tournament-id')
                            ->maxWidth('sm'),

                        TextInput::make('table_id')
                            ->label('Table ID')
                            ->required(fn ($get) => $get('search_by') === self::SEARCH_BY_TABLE_ID)
                            ->visible(fn ($get) => $get('search_by') === self::SEARCH_BY_TABLE_ID)
                            ->autocomplete('hand-export-table-id')
                            ->maxWidth('sm'),

                        TextInput::make('session_hash')
                            ->label('Table session')
                            ->required(fn ($get) => $get('search_by') === self::SEARCH_BY_SESSION_HASH)
                            ->visible(fn ($get) => $get('search_by') === self::SEARCH_BY_SESSION_HASH)
                            ->autocomplete('hand-export-session-hash')
                            ->maxWidth('sm'),

                        Checkbox::make('with_player_hole_cards')
                            ->label('With player hole cards')
                            ->visible(fn ($get) => $get('search_by') === self::SEARCH_BY_PLAYER_ID),

                        Checkbox::make('with_server_info')
                            ->label('With server info'),
                    ])->columns(1),
            ])
            ->statePath('data');
    }

    public function export(): void
    {
        $data = $this->form->getState();

        if (! $this->validateExportParams($data)) {
            Notification::make()
                ->title('Validation Error')
                ->body('Please fill in all required fields.')
                ->danger()
                ->send();

            return;
        }

        try {
            $handLogService = app(HandLogService::class);
            $params = $this->prepareExportParams($data);

            $this->generateHandLogExport($handLogService, $params, $data);
        } catch (Exception $e) {
            Notification::make()
                ->title('Export Failed')
                ->body('An error occurred while exporting hand logs: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }

    private function validateExportParams(array $data): bool
    {
        return match ($data['search_by']) {
            self::SEARCH_BY_PLAYER_ID => ! empty($data['player_id']) && ! empty($data['from']) && ! empty($data['to']),
            self::SEARCH_BY_HAND_ID => ! empty($data['hand_id']),
            self::SEARCH_BY_TOURNAMENT_ID => ! empty($data['tournament_id']),
            self::SEARCH_BY_TABLE_ID => ! empty($data['table_id']),
            self::SEARCH_BY_SESSION_HASH => ! empty($data['session_hash']),
            default => false,
        };
    }

    private function prepareExportParams(array $data): array
    {
        $params = [
            'filter_type' => (int) $data['search_by'],
        ];

        switch ($data['search_by']) {
            case self::SEARCH_BY_PLAYER_ID:
                $params['player_id'] = $data['player_id'];
                $params['from_date'] = $data['from'];
                $params['to_date'] = $data['to'];
                $params['with_player_hole_cards'] = $data['with_player_hole_cards'] ?? false;
                break;
            case self::SEARCH_BY_HAND_ID:
                $params['hand_id'] = $data['hand_id'];
                break;
            case self::SEARCH_BY_TOURNAMENT_ID:
                $params['tour_id'] = $data['tournament_id'];
                break;
            case self::SEARCH_BY_TABLE_ID:
                $params['table_id'] = $data['table_id'];
                break;
            case self::SEARCH_BY_SESSION_HASH:
                $params['table_session'] = $data['session_hash'];
                break;
        }

        return $params;
    }

    private function generateHandLogExport(HandLogService $handLogService, array $params, array $formData): void
    {
        $withServerInfo = $formData['with_server_info'] ?? false;
        $withHoleCards = $formData['with_player_hole_cards'] ?? false;

        $logs = $handLogService->find($params);

        $logsArray = [];
        foreach ($logs as $log) {
            $logsArray[] = $log;
        }

        if (empty($logsArray)) {
            Notification::make()
                ->title('No Data Found')
                ->body('No hand logs found matching the specified criteria.')
                ->warning()
                ->send();

            return;
        }

        // Generate filename with timestamp
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "hand_log_{$timestamp}.txt";

        // Build the content
        $content = '';
        foreach ($logsArray as $log) {
            $text = $log['text'] ?? '';

            if ($params['filter_type'] === (int) self::SEARCH_BY_PLAYER_ID && $withHoleCards) { // Player filter
                $text = 'CoinPoker '.$text;
                // Find player hole cards from SERVER section and add them to the text
                if (preg_match('/\Wpid='.$params['player_id'].'.+name=(?<name>[\w,-]+).+cards=\[(?<cards>\w+)\]/', $text, $matches)) {
                    $text = str_replace(
                        '*** HOLE CARDS ***',
                        "*** HOLE CARDS ***\nDealt to ".$matches['name'].' ['.implode(' ', str_split($matches['cards'], 2)).']',
                        $text
                    );
                }
            }

            if (! $withServerInfo) {
                $text = $this->removeSections(['*** SERVER ***', '*** RNG ***'], $text);
                $text = str_replace(['eur', '€'], '', $text);
            }

            $content .= $text.PHP_EOL;
        }

        $this->dispatch('download-file', [
            'content' => $content,
            'filename' => $filename,
            'contentType' => 'text/plain',
        ]);

        Notification::make()
            ->title('Export Successful')
            ->body('Hand logs exported successfully.')
            ->success()
            ->send();
    }

    private function removeSections(array $sections, string $text): string
    {
        foreach ($sections as $section) {
            $split = explode($section, $text, 2);
            $text = empty($split[0]) ? '' : $split[0];
        }

        return $text;
    }
}
