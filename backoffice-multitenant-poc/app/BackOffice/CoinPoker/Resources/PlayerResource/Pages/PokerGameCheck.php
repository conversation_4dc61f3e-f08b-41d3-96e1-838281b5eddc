<?php

namespace App\BackOffice\CoinPoker\Resources\PlayerResource\Pages;

use App\BackOffice\CoinPoker\DataSources\LegacyPlayerDataSource;
use App\BackOffice\CoinPoker\Pages\ExportLogs;
use App\BackOffice\CoinPoker\Pages\HandReplayer;
use App\BackOffice\CoinPoker\Pages\ViewLogs;
use App\BackOffice\CoinPoker\Resources\PlayerResource;
use App\BackOffice\CoinPoker\Widgets\HandIdSearchWidget;
use App\BackOffice\Shared\CustomListRecords;
use BackedEnum;
use Filament\Actions\Action;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Support\Arr;

class PokerGameCheck extends ListRecords
{
    use CustomListRecords;

    protected static string $resource = PlayerResource::class;

    protected static ?string $title = 'Poker Game Check';

    protected static ?string $slug = 'poker-game-check';

    protected static ?string $navigationLabel = 'Poker Game Check';

    protected static BackedEnum|string|null $navigationIcon = 'heroicon-o-magnifying-glass';

    protected function getHeaderWidgets(): array
    {
        return [
            HandIdSearchWidget::class,
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('table_id')->label('Table ID'),
                TextColumn::make('player_name')->label('Player Nickname'),
                TextColumn::make('from_date')->label('Session Start')->sortable(),
                TextColumn::make('game_ssid')->label('Session Id'),
            ])
            ->query(null)
            ->filters([
                Filter::make('table_id')
                    ->schema([
                        TextInput::make('table_id')->label('Tour. / Table ID')->placeholder('Enter Table ID'),
                    ]),
                Filter::make('nick_name')
                    ->schema([
                        TextInput::make('nick_name')->label('Nickname')->placeholder('Enter Nickname'),
                    ]),
                Filter::make('from')
                    ->schema([
                        DateTimePicker::make('from')->label('From'),
                    ]),
                Filter::make('to')
                    ->schema([
                        DateTimePicker::make('to')->label('To'),
                    ]),
                Filter::make('session_id')
                    ->schema([
                        TextInput::make('session_id')->label('Session ID')->placeholder('Enter Session ID'),
                    ]),
            ], layout: FiltersLayout::AboveContent)
            ->records(function (
                ?array $filters,
                ?string $sortColumn,
                ?string $sortDirection,
                int $page,
                int $recordsPerPage
            ) {
                return app(LegacyPlayerDataSource::class)
                    ->getPokerGameSessions(Arr::collapse($filters), $page, $recordsPerPage, compact('sortColumn', 'sortDirection'));
            })
            ->recordActions([
                Action::make('view_logs')
                    ->label('')
                    ->icon('heroicon-o-eye')
                    ->url(fn ($record) => ViewLogs::getUrl(['session' => $record['game_ssid']]))
                    ->openUrlInNewTab(),
                Action::make('replay')
                    ->label('')
                    ->icon('heroicon-o-play')
                    ->url(fn ($record) => HandReplayer::getUrl(['session' => $record['game_ssid']]))
                    ->openUrlInNewTab(),
                Action::make('export')
                    ->label('')
                    ->icon('heroicon-o-arrow-down')
                    ->url(fn ($record) => ExportLogs::getUrl(['session' => $record['game_ssid']]))
                    ->openUrlInNewTab(),
            ]);
    }
}
