<?php

namespace App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages;

use App\BackOffice\CoinPoker\Repositories\PlayerRepository;
use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource;
use App\BackOffice\CoinPoker\Services\PlayerService;
use App\BackOffice\Shared\CustomListRecords;
use Filament\Actions\Action;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Pagination\LengthAwarePaginator;

class ScreenShoting extends ListRecords
{
    use CustomListRecords;

    protected static string $resource = PlayersTrackingResource::class;

    protected static string|\UnitEnum|null $navigationGroup = 'Players Tracking';

    protected static ?string $navigationLabel = 'Screen Shoting';

    protected static string|\BackedEnum|null $navigationIcon = 'heroicon-o-camera';

    protected static ?int $navigationSort = 3;

    protected static ?string $title = 'Players Tracking - Screen Shoting';

    public function table(Table $table): Table
    {
        return $table
            ->defaultPaginationPageOption(10)
            ->paginationPageOptions([10, 30, 50, 100])
            ->query(null)
            ->headerActions([])
            ->records(function (
                ?string $sortColumn,
                ?string $sortDirection,
                ?string $search,
                int $page,
                int $recordsPerPage
            ): LengthAwarePaginator {
                return app(PlayerRepository::class)->getPlayersTracking(
                    resultSetOptions: [
                        'page' => $page,
                        'perPage' => $recordsPerPage,
                    ],
                    sorting: [
                        'column' => $sortColumn,
                        'direction' => $sortDirection,
                    ]
                );
            })
            ->recordUrl(null)
            ->columns([
                TextColumn::make('player_id')
                    ->label('Id')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('nick_name')
                    ->label('Nickname')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('cur_proxy_id')
                    ->label('Status')
                    ->formatStateUsing(function ($state) {
                        return $state == 0 ? 'Offline' : 'Online';
                    })
                    ->badge()
                    ->color(fn ($state) => $state == 0 ? 'danger' : 'success')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('ip_address')
                    ->label('IP Address')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('last_login')
                    ->label('Last Login')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('login_count')
                    ->label('Login Count')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('crash_count')
                    ->label('Crash Count')
                    ->sortable()
                    ->toggleable(),
                IconColumn::make('banned')
                    ->label('Banned')
                    ->boolean()
                    ->sortable()
                    ->toggleable(),
            ])
            ->recordActions([
                Action::make('view_screenshots')
                    ->label('Screenshots')
                    ->icon('heroicon-o-eye')
                    ->color('primary')
                    ->url(fn ($record) => '#') // TODO: Add route for screenshots
                    ->tooltip('View screenshots'),
                Action::make('view_process')
                    ->label('Process Lists')
                    ->icon('heroicon-o-list-bullet')
                    ->color('info')
                    ->url(fn ($record) => '#') // TODO: Add route for process lists
                    ->tooltip('View process lists'),
                Action::make('disable_tracking')
                    ->label('Disable')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->action(function ($record) {
                        $this->disablePlayerTracking($record);
                    })
                    ->requiresConfirmation()
                    ->modalHeading(fn ($record) => "Disable tracking for player: {$record['nick_name']}")
                    ->modalDescription('This will disable constant screenshoting for this player.')
                    ->modalSubmitActionLabel('Yes, Disable Tracking')
                    ->tooltip('Disable tracking'),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('add_player')
                ->label('Add Player')
                ->icon('heroicon-o-plus')
                ->color('primary')
                ->schema([
                    Textarea::make('player_nicks')
                        ->label('Player Nickname')
                        ->required()
                        ->helperText('Comma separated Nicknames.')
                        ->rows(3),
                    Checkbox::make('use_ids')
                        ->label('Use IDs'),
                ])
                ->action(function (array $data) {
                    $this->addPlayersToTracking($data);
                })
                ->modalHeading('Add Player to Tracking')
                ->modalSubmitActionLabel('Add Players'),
            Action::make('set_timer')
                ->label('Set Timer')
                ->icon('heroicon-o-clock')
                ->color('secondary')
                ->schema([
                    Select::make('timer')
                        ->label('Timer')
                        ->helperText('At Every Chosen Minute')
                        ->maxWidth('xs')
                        ->options([
                            1 => '1',
                            2 => '2',
                            3 => '3',
                            4 => '4',
                            5 => '5',
                            6 => '6',
                            10 => '10',
                            12 => '12',
                            15 => '15',
                            20 => '20',
                            30 => '30',
                        ])
                        ->default($this->getCurrentTimer())
                        ->required()
                        ->selectablePlaceholder(false),
                ])
                ->action(function (array $data) {
                    $this->setPlayersTrackingTimer($data);
                })
                ->modalHeading('Set Players Tracking Timer')
                ->modalSubmitActionLabel('Submit'),
        ];
    }

    public function addPlayersToTracking(array $data): void
    {
        try {
            $playerNicks = $data['player_nicks'] ?? '';
            $useIds = $data['use_ids'] ?? false;

            // Split by comma and trim whitespace
            $nicks = array_map('trim', explode(',', $playerNicks));

            // Validate input
            if (empty($nicks) || (count($nicks) === 1 && empty($nicks[0]))) {
                Notification::make()
                    ->title('Invalid Input')
                    ->body('Please provide valid nicknames or IDs.')
                    ->danger()
                    ->send();

                return;
            }

            // Validation based on use_ids flag
            $errors = [];
            if ($useIds) {
                foreach ($nicks as $id) {
                    if (! is_numeric($id)) {
                        $errors[] = "Invalid ID: '{$id}'";
                    }
                }
            } else {
                foreach ($nicks as $nick) {
                    if (is_numeric($nick)) {
                        $errors[] = "Invalid nick: '{$nick}'";
                    }
                }
            }

            if (! empty($errors)) {
                Notification::make()
                    ->title('Validation Errors')
                    ->body(implode(', ', $errors))
                    ->danger()
                    ->send();

                return;
            }

            $playerService = app(PlayerService::class);
            $successCount = 0;
            $errorCount = 0;
            $notFoundPlayers = [];

            foreach ($nicks as $nick) {
                try {
                    if ($useIds) {
                        $playerId = (int) $nick;
                        // Find player by ID to verify existence
                        $player = app(PlayerRepository::class)->getPlayer($playerId);
                        if (! $player) {
                            $notFoundPlayers[] = "ID: $nick";
                            $errorCount++;

                            continue;
                        }
                    } else {
                        // Find player by nickname using the flexible approach
                        $player = app(PlayerRepository::class)->getPlayer(['nickname' => $nick]);

                        if (! $player) {
                            $notFoundPlayers[] = "Nickname: $nick";
                            $errorCount++;

                            continue;
                        }
                        $playerId = $player['player_id'];
                    }

                    // Enable constant screenshoting for this player
                    $success = $playerService->setConstantScreenshoting($playerId, true);
                    if ($success) {
                        $successCount++;
                    } else {
                        $errorCount++;
                    }
                } catch (\Exception $e) {
                    $errorCount++;
                    report($e);
                }
            }

            // Show result notification
            $message = "Processing completed: {$successCount} players added to tracking";
            if ($errorCount > 0) {
                $message .= ", {$errorCount} errors occurred";
                if (! empty($notFoundPlayers)) {
                    $message .= ' (Players not found: '.implode(', ', $notFoundPlayers).')';
                }
            }

            Notification::make()
                ->title($successCount > 0 ? 'Success' : 'Error')
                ->body($message)
                ->color($successCount > 0 ? 'success' : 'danger')
                ->send();
        } catch (\Exception $e) {
            report($e);
            Notification::make()
                ->title('Error')
                ->body('An error occurred: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function disablePlayerTracking(array $record): void
    {
        try {
            $playerId = $record['player_id'];
            $nickName = $record['nick_name'];

            $success = app(PlayerService::class)->setConstantScreenshoting($playerId, false);

            if (! $success) {
                throw new \Exception("Failed to disable tracking for player: {$nickName}");
            }

            Notification::make()
                ->title('Success')
                ->body("Tracking disabled for player: {$nickName}")
                ->success()
                ->send();
            $this->resetTable();
        } catch (\Exception $e) {
            report($e);
            Notification::make()
                ->title('Error')
                ->body('An error occurred: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getCurrentTimer(): int
    {
        // Get current timer setting, check cache first, then config, default to 5 minutes like in Drupal
        $timer = cache()->get('coinpoker.players_tracking_timer')
            ?? config('coinpoker.players_tracking_timer', '*/5 * * * *');

        // Extract the minute value from the cron pattern (e.g., "*/5" -> "5")
        // Drupal logic: $default_value = substr($timer, 2, 2);
        if (preg_match('/\*\/(\d+)/', $timer, $matches)) {
            return (int) trim($matches[1]);
        }

        // Default fallback
        return 5;
    }

    public function setPlayersTrackingTimer(array $data): void
    {
        try {
            $timerValue = $data['timer'];

            // Create cron pattern like Drupal: $timer = '*/'.$values.' * * * *';
            $cronPattern = "*/{$timerValue} * * * *";

            // Store the timer setting in config (Laravel equivalent of variable_set)
            // In a real implementation, this might need to be stored in database
            // or a config file that can be updated at runtime
            config(['coinpoker.players_tracking_timer' => $cronPattern]);

            // For persistent storage, you might want to use a settings table or cache
            cache()->forever('coinpoker.players_tracking_timer', $cronPattern);

            Notification::make()
                ->title('Success')
                ->body("Tracking timer set to every {$timerValue} minute(s)")
                ->success()
                ->send();

        } catch (\Exception $e) {
            report($e);
            Notification::make()
                ->title('Error')
                ->body('An error occurred: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }
}
