<?php

namespace App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages;

use App\BackOffice\CoinPoker\Repositories\PlayerRepository;
use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource;
use App\BackOffice\CoinPoker\Services\PlayerService;
use Exception;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Facades\FilamentView;

class AddCaptcha extends CreateRecord
{
    protected static string $resource = PlayersTrackingResource::class;

    protected static ?string $title = 'Add Captcha';

    // @todo: this is a good candidate to be moved to a base class and call a custom method like createR<PERSON>ord or whatever
    public function create(bool $another = false): void
    {
        $this->authorizeAccess();

        if ($another) {
            $preserveRawState = $this->preserveFormDataWhenCreatingAnother($this->form->getRawState());
        }

        $this->beginDatabaseTransaction();

        $this->callHook('beforeValidate');
        $data = $this->form->getState();
        $this->callHook('afterValidate');

        $this->handleCaptchaCreation($data);
        $this->callHook('afterCreate');

        $this->commitDatabaseTransaction();

        $this->rememberData();

        if ($another) {
            $this->record = null;
            $this->fillForm();
            $this->form->rawState([
                ...$this->form->getRawState(),
                ...$preserveRawState,
            ]);

            return;
        }

        $this->redirect($redirectUrl = $this->getRedirectUrl(), navigate: FilamentView::hasSpaMode($redirectUrl));
    }

    protected function handleCaptchaCreation(array $data): void
    {
        $entries = array_filter(array_map('trim', explode(',', $data['player_nicks'])));
        $successCount = 0;
        $failedEntries = [];

        $repository = app(PlayerRepository::class);
        $playerService = app(PlayerService::class);
        foreach ($entries as $entry) {
            try {
                if ($data['use_ids'] ?? false) {
                    if (! is_numeric($entry)) {
                        $failedEntries[] = $entry.' (not a valid ID)';

                        continue;
                    }
                    $player = $repository->getPlayers(filters: ['id' => $entry], resultSetOptions: ['take' => 1])->first();
                } else {
                    if (is_numeric($entry)) {
                        $failedEntries[] = $entry.' (should be nickname, not ID)';

                        continue;
                    }
                    $player = $repository->getPlayers(filters: ['nick_name' => $entry], resultSetOptions: ['take' => 1])->first();

                }

                if (! $player) {
                    $failedEntries[] = $entry.' (player not found)';

                    continue;
                }

                $playerService->addCaptcha($player['player_id']);
                $successCount++;
            } catch (Exception $e) {
                $failedEntries[] = $entry.' (error: '.$e->getMessage().')';
            }
        }

        if ($successCount > 0) {
            Notification::make()
                ->title('Captcha Added Successfully')
                ->body("Added captcha for $successCount player(s).")
                ->success()
                ->send();
        }

        if (! empty($failedEntries)) {
            Notification::make()
                ->title('Some entries failed')
                ->body('Failed entries: '.implode(', ', $failedEntries))
                ->warning()
                ->send();
        }
    }

    protected function getRedirectUrl(): string
    {
        return PlayersTrackingResource::getUrl('index');
    }
}
