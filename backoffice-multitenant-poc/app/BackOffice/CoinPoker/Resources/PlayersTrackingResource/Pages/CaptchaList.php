<?php

namespace App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages;

use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource;
use App\BackOffice\Shared\CustomListRecords;
use Filament\Resources\Pages\ListRecords;

class CaptchaList extends ListRecords
{
    use CustomListRecords;

    protected static string $resource = PlayersTrackingResource::class;

    protected static ?string $title = 'Captcha List';
}
