<?php

namespace App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages;

use App\BackOffice\CoinPoker\Repositories\PlayerRepository;
use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource;
use App\BackOffice\Shared\CustomListRecords;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ListRecords;
use Filament\Schemas\Components\Grid;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\HtmlString;
use Livewire\Attributes\Url;

class Heatmap extends ListRecords
{
    use CustomListRecords;

    protected static string $resource = PlayersTrackingResource::class;

    protected static ?string $title = 'Anti-Cheat Heatmap';

    #[Url]
    public ?array $tableFilters = [];

    public function table(Table $table): Table
    {
        return $table
            ->defaultPaginationPageOption(20)
            ->paginationPageOptions([20, 30, 50])
            ->headerActions([])
            ->query(null)
            ->filters([
                Filter::make('player_filter')
                    ->schema([
                        Grid::make(5)->schema([
                            TextInput::make('player_id')
                                ->label('Player ID')
                                ->numeric()
                                ->maxWidth('sm'),
                            TextInput::make('nick_name')
                                ->label('Nickname')
                                ->maxWidth('sm'),
                            Select::make('from_eval')
                                ->label('Min Eval')
                                ->options([
                                    '0' => '0',
                                    '0.1' => '0.1',
                                    '0.2' => '0.2',
                                    '0.3' => '0.3',
                                    '0.4' => '0.4',
                                    '0.5' => '0.5',
                                    '0.6' => '0.6',
                                    '0.7' => '0.7',
                                    '0.8' => '0.8',
                                    '0.9' => '0.9',
                                    '1' => '1',
                                ])
                                ->default('0')
                                ->maxWidth('sm'),
                            Select::make('to_eval')
                                ->label('Max Eval')
                                ->options([
                                    '0.1' => '0.1',
                                    '0.2' => '0.2',
                                    '0.3' => '0.3',
                                    '0.4' => '0.4',
                                    '0.5' => '0.5',
                                    '0.6' => '0.6',
                                    '0.7' => '0.7',
                                    '0.8' => '0.8',
                                    '0.9' => '0.9',
                                    '1' => '1',
                                ])
                                ->default('0.5')
                                ->maxWidth('sm'),
                            TextInput::make('click_count')
                                ->label('Min Clicks')
                                ->numeric()
                                ->default(30)
                                ->maxWidth('sm'),
                        ])->columnSpanFull(),
                    ])
                    ->columnSpanFull(),
            ], layout: FiltersLayout::AboveContent)
            ->records(function (
                ?string $sortColumn,
                ?string $sortDirection,
                ?string $search,
                ?array $filters,
                int $page,
                int $recordsPerPage
            ): LengthAwarePaginator {
                return app(PlayerRepository::class)->getPlayersHeatmap(
                    filters: array_merge(
                        Arr::collapse($filters ?? []),
                        ! empty($search) ? ['search' => $search] : []
                    ),
                    resultSetOptions: [
                        'page' => $page,
                        'perPage' => $recordsPerPage,
                    ],
                    sorting: [
                        'column' => $sortColumn,
                        'direction' => $sortDirection,
                    ]
                );
            })
            ->recordActions([
                ViewAction::make()
                    ->url(fn ($record) => PlayerHeatmapDetails::getUrl(['player' => $record['player_id']]))
                    ->label('View all heatmaps')
                    ->authorize(true)
                    ->icon('heroicon-o-eye'),
            ])
            ->recordUrl(null)
            ->columns([
                TextColumn::make('player_id')
                    ->label('Player ID')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('nick_name')
                    ->label('Nickname')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('min_eval_result')
                    ->label('Evaluation')
                    ->formatStateUsing(function ($state, $record) {
                        if ($state < 0.6) {
                            return new HtmlString('<span style="color: red; font-weight: bold;">'.$state.'</span>');
                        }

                        return $state;
                    })
                    ->sortable()
                    ->toggleable(),
            ]);
    }

    protected function getTableFilters(): array
    {
        return [];
    }
}
