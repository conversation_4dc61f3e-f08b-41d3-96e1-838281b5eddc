<?php

namespace App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages;

use App\BackOffice\CoinPoker\Models\GamingSystem\Provider;
use App\BackOffice\CoinPoker\Repositories\PlayerRepository;
use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource;
use App\BackOffice\CoinPoker\Services\AccountingApiService;
use App\BackOffice\CoinPoker\Services\PlayerService;
use App\BackOffice\Shared\CustomListRecords;
use Carbon\Carbon;
use Filament\Actions\Action;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Arr;

class MultiAccountCheck extends ListRecords
{
    use CustomListRecords;

    protected static string $resource = PlayersTrackingResource::class;

    protected static ?string $title = 'Multi Account Check';

    public bool $isDeviceMode = true;

    public function mount(): void
    {
        parent::mount();

        $this->updateDeviceMode(
            $this->tableFilters['count_by_devices']['count_by_devices'] ?? 'devices'
        );
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('hash')
                    ->label('Device Hash')
                    ->visible(fn () => $this->isDeviceMode),

                TextColumn::make('confirmed_count')
                    ->label('Confirmed Accounts')
                    ->default('-')
                    ->visible(fn () => $this->isDeviceMode),

                TextColumn::make('quota')
                    ->label('Quota')
                    ->default('-')
                    ->visible(fn () => $this->isDeviceMode),

                TextColumn::make('banned')
                    ->label('Banned')
                    ->default('No')
                    ->visible(fn () => $this->isDeviceMode),

                // IP-based columns (when count_by_devices === 'ip')
                TextColumn::make('ip')
                    ->label('IP')
                    ->formatStateUsing(fn ($state) => long2ip($state))
                    ->visible(fn () => ! $this->isDeviceMode),

                TextColumn::make('cnt')
                    ->label('Count')
                    ->visible(fn () => ! $this->isDeviceMode),
            ])
            ->query(null)
            ->records(function (
                ?array $filters,
                ?string $sortColumn,
                ?string $sortDirection,
                int $page,
                int $recordsPerPage
            ) {
                return app(PlayerRepository::class)->getMultiAccountData(
                    filters: Arr::collapse($filters ?? []),
                    page: $page,
                    recordsPerPage: $recordsPerPage
                );
            })
            ->recordActions([
                // Device based actions
                Action::make('expand_device_players')
                    ->label('View Players')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->visible(fn () => $this->isDeviceMode)
                    ->modalContent(fn ($record) => $this->getDeviceExpandedView($record))
                    ->modalWidth('5xl')
                    ->modalSubmitAction(false),

                Action::make('change_quota')
                    ->label('Change Quota')
                    ->icon('heroicon-o-cog-6-tooth')
                    ->color('warning')
                    ->visible(fn () => $this->isDeviceMode)
                    ->schema([
                        TextInput::make('quota')
                            ->label('Quota')
                            ->maxWidth('xs')
                            ->required()
                            ->numeric()
                            ->minValue(0)
                            ->helperText('Set the device quota limit')
                            ->default(fn ($record) => $record['quota'] ?? 0),
                    ])
                    ->action(function (array $data, $record) {
                        $this->changeDeviceQuota($record, $data['quota']);
                    })
                    ->modalHeading(fn ($record) => "Change quota for device: {$record['hash']}")
                    ->modalDescription(fn ($record) => "(Current: {$record['quota']})")
                    ->modalSubmitActionLabel('Save Quota'),

                // IP-based actions
                Action::make('ban')
                    ->label('Ban Players')
                    ->icon('heroicon-o-no-symbol')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->visible(fn () => ! $this->isDeviceMode)
                    ->modalHeading(fn ($record) => 'Ban All Players Using This IP')
                    ->modalDescription(fn ($record) => 'This will ban ALL players who have used IP address: '.long2ip($record['ip'])."\n\nThis action cannot be undone. Are you sure you want to proceed?")
                    ->modalSubmitActionLabel('Yes, Ban All Players')
                    ->modalCancelActionLabel('Cancel')
                    ->action(function (array $data, $record) {
                        $this->banPlayersByIp($record);
                    }),

                Action::make('expand')
                    ->label('View Players')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->visible(fn () => ! $this->isDeviceMode)
                    ->modalContent(fn ($record) => $this->getExpandedView($record['player_ids_with_same_ip'] ?? []))
                    ->modalSubmitAction(false)
                    ->modalWidth('5xl'),
            ])
            ->filters([
                Filter::make('count_by_devices')
                    ->schema([
                        Select::make('count_by_devices')
                            ->label('Tracking Type')
                            ->options([
                                'devices' => 'Devices',
                                'ip' => 'IP',
                            ])
                            ->default('devices')
                            ->selectablePlaceholder(false)
                            ->required()
                            ->live()
                            ->afterStateUpdated(fn ($state) => $this->updateDeviceMode($state)),
                    ]),
                Filter::make('ip')
                    ->schema([
                        Textarea::make('ip')
                            ->label('IP / Device Hashes')
                            ->helperText('Comma separated IPs or device hashes')
                            ->rows(1),
                    ]),
                Filter::make('player_id')
                    ->schema([
                        TextInput::make('player_id')
                            ->label('Player ID')
                            ->numeric(),
                    ]),
                Filter::make('from_date')
                    ->schema([
                        DateTimePicker::make('from_date')
                            ->label('From Login Date')
                            ->required()
                            ->default(Carbon::now()->subDays(10))
                            ->displayFormat('Y-m-d H:i'),
                    ]),
                Filter::make('acc_cnt')
                    ->schema([
                        Select::make('acc_cnt')
                            ->label('Confirmed Accounts >=')
                            ->options([
                                1 => '1',
                                2 => '2',
                                3 => '3',
                                4 => '4',
                                6 => '6',
                                10 => '10',
                                30 => '30',
                                50 => '50',
                                100 => '100',
                            ])
                            ->default(4)
                            ->selectablePlaceholder(false)
                            ->required(),
                    ]),
            ], layout: FiltersLayout::AboveContent)
            ->paginated([20, 50, 100])
            ->headerActions([])
            ->defaultPaginationPageOption(20)
            ->emptyStateHeading('No data found')
            ->emptyStateDescription('Apply filters to view multi-account data.');
    }

    public function updateDeviceMode(?string $state): void
    {

        $this->isDeviceMode = ($state ?? 'devices') === 'devices';
    }

    public function changeDeviceQuota(array $record, int $quota): void
    {
        try {
            $deviceId = $record['id'];

            if (! $deviceId) {
                Notification::make()
                    ->title('Error')
                    ->body('Device ID not found')
                    ->danger()
                    ->send();

                return;
            }

            // @todo: this is failing yet in API side, pending to fix the API
            //            $result = app(AccountingApiService::class)->setDeviceQuota($deviceId, $quota);

            Notification::make()
                ->title('Error')
                ->body('This feature is yet failing in API side, and in Drupal, pending to fix the API')
                ->success()
                ->send();

            return;

            if ($result) {
                Notification::make()
                    ->title('Success')
                    ->body("Quota changed successfully for device: {$record['hash']}")
                    ->success()
                    ->send();
            } else {
                throw new \Exception('Failed to change quota');
            }
        } catch (\Exception $exception) {
            report($exception);
            Notification::make()
                ->title('Error')
                ->body('An error occurred: '.$exception->getMessage())
                ->danger()
                ->send();
        }
    }

    public function banPlayersByIp(array $record): void
    {
        try {
            $ip = $record['ip'];
            $ipAddress = long2ip($ip);

            if (! $ip) {
                Notification::make()
                    ->title('Error')
                    ->body('Invalid IP address')
                    ->danger()
                    ->send();

                return;
            }

            $bannedPlayers = app(PlayerService::class)->banPlayersByIp($ip);

            if (empty($bannedPlayers)) {
                Notification::make()
                    ->title('No Players Found')
                    ->body("No players found using IP address: {$ipAddress} (or IP doesn't meet minimum threshold of 4+ distinct players)")
                    ->warning()
                    ->send();

                return;
            }

            $newlyBannedCount = collect($bannedPlayers)->where('was_already_banned', false)->count();
            $alreadyBannedCount = collect($bannedPlayers)->where('was_already_banned', true)->count();

            $message = 'Successfully processed '.count($bannedPlayers)." players for IP {$ipAddress}:";
            if ($newlyBannedCount > 0) {
                $message .= "\n- {$newlyBannedCount} players newly banned";
            }
            if ($alreadyBannedCount > 0) {
                $message .= "\n- {$alreadyBannedCount} players were already banned";
            }

            Notification::make()
                ->title('Ban Action Completed')
                ->body($message)
                ->success()
                ->send();
        } catch (\Exception $e) {
            Notification::make()
                ->title('Error')
                ->body('An error occurred: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getExpandedView(array $playerIds): View
    {
        if (empty($playerIds)) {
            $players = collect();
        } else {
            $players = app(PlayerRepository::class)->getPlayers(
                filters: ['player_ids' => $playerIds],
                sorting: ['column' => 'banned', 'direction' => 'asc']
            )->map(function ($player) {
                return [
                    'player_id' => $player['player_id'],
                    'nick_name' => $player['nick_name'],
                    'email' => $player['email'],
                    'balance_total' => $player['balance_money'],
                    'banned' => $player['banned'] ? 'Yes' : 'No',
                ];
            });
        }

        return view('filament.pages.multi-account-expanded', compact('players', 'playerIds'));
    }

    protected function getDeviceExpandedView(array $record): View
    {
        $providerPlayerIds = app(AccountingApiService::class)->getDevicePlayers($record['hash'], $this->tableFilters['from_date']['from_date'] ?? null);

        if (empty($providerPlayerIds)) {
            $players = collect();
        } else {
            $players = app(PlayerRepository::class)->getPlayers(
                filters: ['player_provider_ids' => $providerPlayerIds, 'provider_name' => Provider::COINPOKER_PROVIDER_NAME],
                sorting: ['column' => 'player_id', 'direction' => 'desc']
            )->map(function ($player) {
                return [
                    'player_id' => $player['player_id'],
                    'nick_name' => $player['nick_name'],
                    'email' => $player['email'],
                    'balance_total' => $player['balance_money'],
                    'banned' => $player['banned'] ? 'Yes' : 'No',
                ];
            });
        }

        return view('filament.pages.multi-account-device-expanded', ['players' => $players, 'deviceHash' => $record['hash']]);
    }

    public function togglePlayerBan(int $playerId, bool $shouldBan): void
    {
        try {
            $player = app(PlayerRepository::class)->getPlayer($playerId);

            if (! $player) {
                Notification::make()
                    ->title('Error')
                    ->body('Player not found')
                    ->danger()
                    ->send();

                return;
            }

            $success = app(PlayerService::class)->updatePlayerBanStatus($playerId, $shouldBan);

            if (! $success) {
                throw new \Exception('Failed to update player ban');
            }

            $action = $shouldBan ? 'banned' : 'unbanned';
            Notification::make()
                ->title('Success')
                ->body("Player {$player['nick_name']} has been $action successfully")
                ->success()
                ->send();
        } catch (\Exception $e) {
            report($e);
            Notification::make()
                ->title('Error')
                ->body('An error occurred while updating player ban status: '.$e->getMessage())
                ->danger()
                ->send();
        }
    }
}
