<?php

namespace App\BackOffice\CoinPoker\Resources\RestrictionGroups;

use App\BackOffice\CoinPoker\Repositories\PlayerRepository;
use App\BackOffice\CoinPoker\Resources\RestrictionGroups\Pages\CreateRestrictionGroup;
use App\BackOffice\CoinPoker\Resources\RestrictionGroups\Pages\EditRestrictionGroup;
use App\BackOffice\CoinPoker\Resources\RestrictionGroups\Pages\ListRestrictionGroups;
use App\BackOffice\CoinPoker\Services\PlayerService;
use App\BackOffice\Shared\Resources\TenantBaseResource;
use BackedEnum;
use Filament\Actions\Action;
use Filament\Actions\EditAction;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;

class RestrictionGroupResource extends TenantBaseResource
{
    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')->required(),
                Select::make('players')
                    ->label('Players')
                    ->multiple()
                    ->options(fn ($get) => static::getPlayerOptionsByPlayerIds((array) $get('players')))
                    ->getSearchResultsUsing(fn ($search) => static::getPlayerSearchResults($search))
                    ->in(fn () => null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table->defaultPaginationPageOption(15)
            ->filters([
                Filter::make('name')
                    ->schema([
                        TextInput::make('name')
                            ->label('Name')
                            // @todo: improvement, if we switch to deferred, we have to apply this
                            ->extraAttributes(['x-on:keydown.enter.prevent.stop' => '$wire.dispatch(\'applyFilters\')'])
                            ->debounce(600),
                    ]),
                Filter::make('players')
                    ->schema([
                        Select::make('players')
                            ->label('Players')
                            ->placeholder('Start typing to search players')
                            ->options(fn ($get) => static::getPlayerOptionsByPlayerIds((array) $get('players')))
                            ->getSearchResultsUsing(fn ($search) => static::getPlayerSearchResults($search))
                            ->debounce(50)
                            ->multiple()
                            ->searchable(),
                    ]),
            ], layout: FiltersLayout::AboveContent)
            ->query(null)
            ->records(function (
                ?string $sortColumn,
                ?string $sortDirection,
                ?array $filters,
                int $page,
                int $recordsPerPage
            ): LengthAwarePaginator {
                return app(PlayerRepository::class)
                    ->getRestrictionGroups(Arr::collapse($filters), $page, $recordsPerPage, [
                        'column' => $sortColumn,
                        'direction' => $sortDirection,
                    ]);
            })
            ->paginationPageOptions([15, 30, 50, 100])
            ->recordUrl(null)
            ->columns([
                TextColumn::make('name')
                    ->sortable(),
                TextColumn::make('players')
                    ->getStateUsing(function ($record) {
                        if (empty($record['players'])) {
                            return '-';
                        }

                        return collect($record['players'])->implode(', ');
                    }),
            ])
            ->recordActions([
                EditAction::make()
                    ->url(fn ($record) => static::getUrl('edit', ['restrictionGroup' => $record['id']]))
                    ->label('')
                    ->authorize(true)
                    ->modalHeading('Edit')
                    ->slideOver()
                    ->icon('heroicon-o-pencil'),
                Action::make('delete')
                    ->label('')
                    ->icon('heroicon-o-trash')
                    ->color('danger')
                    ->modalDescription('Are you sure you want to delete this restriction group? This action cannot be undone.')
                    ->modalHeading('Delete Restriction Group')
                    ->requiresConfirmation()
                    ->action(function (array $record, $livewire) {
                        try {
                            app(PlayerService::class)->deleteRestrictionGroups([$record['id']]);
                            $livewire->resetTable();
                            Notification::make()->title('Restriction group deleted')->success()->send();
                        } catch (\Exception $exception) {
                            report($exception);
                            Notification::make()
                                ->title('Error deleting restriction group')
                                ->danger()
                                ->send();
                        }
                    }),
            ]);
    }

    private static function getPlayerOptionsByPlayerIds(array $playerIds): array
    {
        if (empty($playerIds)) {
            return [];
        }

        return app(PlayerRepository::class)
            ->getPlayers(filters: ['player_ids' => $playerIds])
            ->pluck('nick_name', 'player_id')
            ->toArray();
    }

    private static function getPlayerSearchResults(?string $search): array
    {
        if (empty($search)) {
            return [];
        }

        return app(PlayerRepository::class)
            ->getPlayers(filters: compact('search'), resultSetOptions: ['limit' => 20])
            ->pluck('nick_name', 'player_id')
            ->toArray();
    }

    public static function getPages(): array
    {
        return [
            'index' => ListRestrictionGroups::route('/'),
            'create' => CreateRestrictionGroup::route('/create'),
            'edit' => EditRestrictionGroup::route('/{restrictionGroup}/edit'),
        ];
    }

    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }
}
