<?php

namespace App\BackOffice\CoinPoker\Resources\RestrictionGroups\Pages;

use App\BackOffice\CoinPoker\Resources\RestrictionGroups\RestrictionGroupResource;
use App\BackOffice\CoinPoker\Services\PlayerService;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Facades\FilamentView;

class CreateRestrictionGroup extends CreateRecord
{
    protected static string $resource = RestrictionGroupResource::class;

    public function create(bool $another = false): void
    {
        $this->authorizeAccess();

        if ($another) {
            $preserveRawState = $this->preserveFormDataWhenCreatingAnother($this->form->getRawState());
        }

        $this->beginDatabaseTransaction();

        $this->callHook('beforeValidate');
        $data = $this->form->getState();
        $this->callHook('afterValidate');

        app(PlayerService::class)->createRestrictionGroup($data);
        $this->callHook('afterCreate');

        $this->commitDatabaseTransaction();

        $this->rememberData();

        $this->getCreatedNotification()?->send();

        if ($another) {
            // Ensure that the form record is anonymized so that relationships aren't loaded.
            //            $this->form->model($this->getRecord()::class);
            $this->record = null;

            $this->fillForm();

            $this->form->rawState([
                ...$this->form->getRawState(),
                ...$preserveRawState,
            ]);

            return;
        }

        $this->redirect($redirectUrl = $this->getRedirectUrl(), navigate: FilamentView::hasSpaMode($redirectUrl));
    }

    protected function getRedirectUrl(): string
    {
        return route('filament.admin.resources.restriction-groups.index');
    }
}
