<?php

namespace App\BackOffice\CoinPoker\Resources\RestrictionGroups\Pages;

use App\BackOffice\CoinPoker\Resources\RestrictionGroups\RestrictionGroupResource;
use App\BackOffice\CoinPoker\Services\PlayerService;
use Filament\Actions\DeleteAction;

class EditRestrictionGroup extends CustomEditRecord
{
    protected static function resource(): string
    {
        return RestrictionGroupResource::class;
    }

    public function view(): string
    {
        return 'filament.resources.restriction-groups.pages.edit-restriction-group';
    }

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }

    public static function getResource(): string
    {
        return RestrictionGroupResource::class;
    }

    public function mount($restrictionGroup): void
    {
        $this->record = $restrictionGroup;
        $this->form->fill([
            'name' => $this->record['name'],
            'players' => array_keys($this->record['players'] ?? []),
        ]);
    }

    protected function performSave($validatedData)
    {
        app(PlayerService::class)
            ->updateRestrictionGroup($this->record->id, $validatedData);
    }

    protected function getRedirectUrl(): ?string
    {
        return ListRestrictionGroups::getUrl();
    }
}
