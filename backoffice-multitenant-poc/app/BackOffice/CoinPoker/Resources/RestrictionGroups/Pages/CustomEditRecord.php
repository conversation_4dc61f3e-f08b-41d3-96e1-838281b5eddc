<?php

namespace App\BackOffice\CoinPoker\Resources\RestrictionGroups\Pages;

use Filament\Resources\Pages\EditRecord;
use Filament\Support\Facades\FilamentView;

abstract class CustomEditRecord extends EditRecord
{
    abstract protected function performSave($validatedData);

    public static function getResource(): string
    {
        return static::resource();
    }

    abstract protected static function resource(): string;

    public function getView(): string
    {
        return $this->view();
    }

    abstract public function view(): string;

    public function save(bool $shouldRedirect = true, bool $shouldSendSavedNotification = true): void
    {
        $this->authorizeAccess();

        $this->callHook('beforeValidate');

        $validatedData = $this->form->getState(afterValidate: function (): void {
            $this->callHook('afterValidate');
            $this->callHook('beforeSave');
        });

        $this->performSave($validatedData);

        $this->callHook('afterSave');

        $this->rememberData();

        if ($shouldSendSavedNotification) {
            $this->getSavedNotification()?->send();
        }

        if ($shouldRedirect && ($redirectUrl = $this->getRedirectUrl())) {
            $this->redirect($redirectUrl, navigate: FilamentView::hasSpaMode($redirectUrl));
        }
    }
}
