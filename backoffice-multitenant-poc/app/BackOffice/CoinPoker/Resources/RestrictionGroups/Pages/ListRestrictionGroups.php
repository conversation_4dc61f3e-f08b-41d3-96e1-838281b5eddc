<?php

namespace App\BackOffice\CoinPoker\Resources\RestrictionGroups\Pages;

use App\BackOffice\CoinPoker\Resources\RestrictionGroups\RestrictionGroupResource;
use App\BackOffice\Shared\CustomListRecords;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListRestrictionGroups extends ListRecords
{
    use CustomListRecords;

    protected static string $resource = RestrictionGroupResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
