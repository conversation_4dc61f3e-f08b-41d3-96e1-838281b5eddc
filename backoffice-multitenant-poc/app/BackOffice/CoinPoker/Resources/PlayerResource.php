<?php

namespace App\BackOffice\CoinPoker\Resources;

use App\BackOffice\CoinPoker\Repositories\PlayerRepository;
use App\BackOffice\CoinPoker\Resources\PlayerResource\Pages\HandExport;
use App\BackOffice\CoinPoker\Resources\PlayerResource\Pages\ListPlayers;
use App\BackOffice\CoinPoker\Resources\PlayerResource\Pages\PokerGameCheck;
use App\BackOffice\CoinPoker\Resources\PlayerResource\Pages\ViewPlayer;
use App\BackOffice\CoinPoker\Services\AccountingApiService;
use App\BackOffice\CoinPoker\Services\TagsService;
use App\BackOffice\Shared\Resources\TenantBaseResource;
use Filament\Actions\ViewAction;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Components\Group;
use Filament\Schemas\Components\Section;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Illuminate\Support\HtmlString;

class PlayerResource extends TenantBaseResource
{
    protected static ?string $navigationLabel = 'Players';

    protected static ?string $modelLabel = 'Player';

    protected static ?string $pluralModelLabel = 'Players';

    protected static string|\UnitEnum|null $navigationGroup = 'Players';

    protected static ?int $navigationSort = 1;

    public static function getNavigationLabel(): string
    {
        return 'Players List';
    }

    public static function getNavigationGroup(): ?string
    {
        return 'Players';
    }

    public static function table(Table $table): Table
    {
        return $table
            ->filters([
                Filter::make('basic_filters')
                    ->columnSpanFull()
                    ->schema([
                        Group::make()
                            ->columns(3)
                            ->schema([
                                TextInput::make('player_id')
                                    ->label('Player ID')
                                    ->placeholder('Enter Player ID...'),
                                TextInput::make('nick_name')
                                    ->label('Nickname')
                                    ->placeholder('Enter nickname...'),
                                TextInput::make('email')
                                    ->label('Email')
                                    ->placeholder('Enter email...'),
                            ]),
                        Group::make()
                            ->columns(3)
                            ->schema([
                                TextInput::make('player_provider_id')
                                    ->label('Player Provider Id (Provider Table)')
                                    ->placeholder('Enter player provider id...'),
                                TextInput::make('phone_number')
                                    ->label('Phone Number (Provider Table)')
                                    ->placeholder('Enter phone number...'),
                            ]),
                    ]),
            ], layout: FiltersLayout::AboveContent)
            ->defaultPaginationPageOption(10)
            ->paginationPageOptions([10, 30, 50, 100])
            ->query(null)
            ->records(function (
                ?string $sortColumn,
                ?string $sortDirection,
                ?string $search,
                ?array $filters,
                int $page,
                int $recordsPerPage
            ): LengthAwarePaginator {
                return app(PlayerRepository::class)->getPlayers(
                    filters: array_merge(
                        Arr::collapse($filters),
                        ! empty($search) ? ['search' => $search] : []
                    ),
                    resultSetOptions: [
                        'page' => $page,
                        'perPage' => $recordsPerPage,
                    ],
                    sorting: [
                        'column' => $sortColumn,
                        'direction' => $sortDirection,
                    ],
                    extra: [
                        'with' => ['tags', 'restrictedGroups'],
                    ]
                );
            })
            ->recordUrl(null)
            ->columns([
                TextColumn::make('player_id')
                    ->label('Player ID')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('nick_name')
                    ->label('Nickname')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('email')
                    ->label('Email')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('balance_money')
                    ->label('Balance USD')
                    ->money('USD')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('cur_proxy_id')
                    ->label('Status')
                    ->formatStateUsing(function ($state) {
                        return $state == 0 ? 'Offline' : 'Online';
                    })
                    ->badge()
                    ->color(fn ($state) => $state == 0 ? 'danger' : 'success')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('cur_ip')
                    ->label('IP Address')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('last_login')
                    ->label('Last Login')
                    ->date()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('login_count')
                    ->label('Login Count')
                    ->sortable()
                    ->toggleable(),
                IconColumn::make('banned')
                    ->label('Banned')
                    ->boolean()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('tags')
                    ->label('Tags')
                    ->formatStateUsing(function (?string $state): HtmlString {
                        if (! $state) {
                            return new HtmlString('<span class="fi-badge fi-color-gray">No tags</span>');
                        }

                        // Get tag names using TagsService (matches Drupal's tagsToHtml logic)
                        $tagNames = TagsService::getTagNames($state);

                        if (empty($tagNames)) {
                            return new HtmlString('<span class="fi-badge fi-color-gray">No tags</span>');
                        }

                        $badges = [];
                        foreach ($tagNames as $tagName) {
                            $badges[] = '<span class="fi-badge fi-color-primary" style="margin-right: 4px;">'.htmlspecialchars($tagName).'</span>';
                        }

                        return new HtmlString(implode('', $badges));
                    })
                    ->html()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('restricted_groups')
                    ->label('Restricted Groups')
                    ->formatStateUsing(function ($record): HtmlString {
                        $restrictedGroups = $record['restricted_groups'] ?? [];
                        if (! empty($restrictedGroups)) {
                            $badges = [];
                            foreach ($restrictedGroups as $group) {
                                $groupName = is_array($group) ? ($group['name'] ?? 'Unknown') : $group;
                                $badges[] = '<span class="fi-badge fi-color-warning" style="margin-right: 4px;">'.htmlspecialchars($groupName).'</span>';
                            }

                            return new HtmlString(implode('', $badges));
                        }

                        return new HtmlString('<span class="fi-badge fi-color-gray">-</span>');
                    })
                    ->html()
                    ->sortable()
                    ->toggleable(),
            ])
            ->recordActions([
                ViewAction::make()
                    ->url(fn ($record) => ViewPlayer::getUrl([$record['player_id']]))
                    ->label('View')
                    ->authorize(true)
                    ->icon('heroicon-o-eye'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListPlayers::route('/'),
            'poker-game-check' => PokerGameCheck::route('/poker-game-check'),
            'hand-export' => HandExport::route('/hand-export'),
            'view' => ViewPlayer::route('/{record}'),
        ];
    }

    public static function infolist(Schema $schema): Schema
    {
        $getApiData = function ($record) {
            static $cache = [];
            $providerId = $record->player_provider_id ?? $record['player_provider_id'] ?? null;
            if (! $providerId) {
                return null;
            }
            if (! isset($cache[$providerId])) {
                $api = App::make(AccountingApiService::class);
                $cache[$providerId] = $api->getPlayerInfo($providerId);
            }

            return $cache[$providerId] ?? null;
        };

        return $schema
            ->schema([
                Section::make('Player Profile')
                    ->columns(3)
                    ->schema([
                        ImageEntry::make('avatar')
                            ->label('Avatar')
                            ->imageSize(200)
                            ->circular()
                            ->state(function ($record) use ($getApiData) {
                                $apiData = $getApiData($record);
                                // @todo: this is a temporary fix to get the avatar url
                                $avatar = 'http://dev.api.coinpoker.com:5013/avatars/'.$apiData['profile']['avatar'];

                                if (! $avatar) {
                                    return 'https://icons.iconarchive.com/icons/papirus-team/papirus-status/512/avatar-default-icon.png';
                                }

                                // If avatar is already a full URL, use it directly
                                if (filter_var($avatar, FILTER_VALIDATE_URL)) {
                                    return $avatar;
                                }

                                // Otherwise, prepend the base URL
                                $base = config('services.coinpoker.accounting_api.avatar_base_url') ?? '';
                                $fullUrl = $base.$avatar;

                                // Validate the constructed URL
                                if (filter_var($fullUrl, FILTER_VALIDATE_URL)) {
                                    return $fullUrl;
                                }

                                // Fallback to default if URL construction fails
                                return 'https://icons.iconarchive.com/icons/papirus-team/papirus-status/512/avatar-default-icon.png';
                            }),
                        Group::make()
                            ->columnSpan(2)
                            ->columns(2)
                            ->schema([
                                TextEntry::make('nick_name')
                                    ->label('Nickname')
                                    ->weight(10),
                                TextEntry::make('player_provider_id')
                                    ->label('External ID')
                                    ->weight(20),
                                TextEntry::make('balance_usdt')
                                    ->label('Balance USDT')
                                    ->money('USD')
                                    ->weight(30)
                                    ->state(function ($record) use ($getApiData) {
                                        $apiData = $getApiData($record);

                                        return $apiData['profile']['balance'] ?? null;
                                    }),
                                TextEntry::make('balance_chp')
                                    ->label('Balance CHP')
                                    ->money('USD')
                                    ->weight(40)
                                    ->state(function ($record) use ($getApiData) {
                                        $apiData = $getApiData($record);

                                        return $apiData['profile']['balance_chp'] ?? null;
                                    }),
                                TextEntry::make('email')
                                    ->label('Email')
                                    ->weight(50),
                                TextEntry::make('affiliate_id')
                                    ->label('Affiliate ID')
                                    ->state(function ($record) use ($getApiData) {
                                        $apiData = $getApiData($record);

                                        return $apiData['profile']['affiliate_id'] ?? null;
                                    }),
                                TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(function ($state) {
                                        return $state === 'Online' ? 'success' : 'danger';
                                    })
                                    ->state(function ($record) {
                                        return $record->cur_proxy_id == 0 ? 'Offline' : 'Online';
                                    })
                                    ->weight(70),
                                TextEntry::make('banned')
                                    ->label('Banned')
                                    ->badge()
                                    ->color(function ($state) {
                                        return $state ? 'danger' : 'success';
                                    })
                                    ->state(function ($record) {
                                        return $record->banned ? 'Yes' : 'No';
                                    })
                                    ->weight(80),
                            ]),
                    ]),
                Section::make('Account Settings')
                    ->columns(2)
                    ->schema([
                        TextEntry::make('chat_blocked')
                            ->label('Chat Blocked')
                            ->badge()
                            ->color(function ($state) {
                                return $state ? 'danger' : 'success';
                            })
                            ->state(function ($record) {
                                return $record->chat_blocked ? 'Yes' : 'No';
                            }),
                        TextEntry::make('collusion_check')
                            ->label('Collusion Check')
                            ->badge()
                            ->color(function ($state) {
                                return $state ? 'success' : 'warning';
                            })
                            ->state(function ($record) {
                                return $record->collusion_check ? 'Enabled' : 'Disabled';
                            }),
                        TextEntry::make('restricted_groups')
                            ->label('Restricted groups')
                            ->state(function ($record) {
                                // Get the player account model to access the relationship
                                $restrictedGroups = $record['restricted_groups'] ?? [];
                                if (! empty($restrictedGroups)) {
                                    $groupNames = [];
                                    foreach ($restrictedGroups as $group) {
                                        $groupNames[] = is_array($group) ? ($group['name'] ?? 'Unknown') : $group;
                                    }

                                    return implode(', ', $groupNames);
                                }

                                return '-';
                            }),
                        TextEntry::make('created_at')
                            ->label('Created')
                            ->dateTime()
                            ->state(function ($record) use ($getApiData) {
                                $apiData = $getApiData($record);
                                $created = $apiData['profile']['created_at'] ?? null;

                                return $created ? \Carbon\Carbon::parse($created)->toDate() : null;
                            }),
                        TextEntry::make('referrer')
                            ->label('Referrer')
                            ->state(function ($record) use ($getApiData) {
                                $apiData = $getApiData($record);
                                $refId = $apiData['profile']['referrer_id'] ?? null;
                                $refNick = $apiData['profile']['referrer_nick'] ?? null;
                                if ($refId && $refNick) {
                                    return $refNick.' (ID: '.$refId.')';
                                } elseif ($refId) {
                                    return 'Provider player id: '.$refId;
                                }

                                return '-';
                            }),
                    ]),
                Section::make('Wallets')
                    ->schema([
                        TextEntry::make('wallets')
                            ->label('Wallets')
                            ->state(function ($record) use ($getApiData) {
                                $apiData = $getApiData($record);
                                $wallets = $apiData['wallets'] ?? [];
                                if (! $wallets) {
                                    return '-';
                                }
                                $lines = [];
                                foreach ($wallets as $wallet) {
                                    $platform = $wallet['platform_name'] ?? ($wallet['service_platform_id'] ?? '');
                                    $address = $wallet['address'] ?? '';
                                    $confirmed = $wallet['confirmed_at'] ?? '-';
                                    $lines[] = "$platform: $address confirmed: $confirmed";
                                }

                                return implode('<br>', $lines);
                            })
                            ->html()
                            ->extraAttributes(['class' => 'whitespace-pre-line']),
                    ]),
                Section::make('Wallets V2')
                    ->schema([
                        TextEntry::make('wallets_v2')
                            ->label('Wallets V2')
                            ->state(function ($record) use ($getApiData) {
                                $apiData = $getApiData($record);
                                $wallets = $apiData['wallets_v2'] ?? [];
                                if (! $wallets) {
                                    return '-';
                                }
                                $lines = [];
                                foreach ($wallets as $wallet) {
                                    $chain = $wallet['chain'] ?? '';
                                    $address = $wallet['address'] ?? '';
                                    $lines[] = "$chain: $address";
                                }

                                // @todo: improve this
                                return implode('<br>', $lines);
                            })
                            ->html()
                            ->extraAttributes(['class' => 'whitespace-pre-line']),
                    ]),
                // @todo: include a section here for tags Groups and Notes replicating what we have in Drupal under the list of details
                // maybe the approach is to prepend a widget in ViewPlayer::getFooterWidgets()
            ]);
    }
}
