<?php

namespace App\BackOffice\CoinPoker\Resources;

use App\BackOffice\CoinPoker\Repositories\PlayerRepository;
use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages\AddCaptcha;
use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages\CaptchaList;
use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages\Heatmap;
use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages\MultiAccountCheck;
use App\BackOffice\CoinPoker\Resources\PlayersTrackingResource\Pages\ScreenShoting;
use App\BackOffice\Shared\Resources\TenantBaseResource;
use BackedEnum;
use Filament\Actions\Action;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Textarea;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Pagination\LengthAwarePaginator;

class PlayersTrackingResource extends TenantBaseResource
{
    protected static BackedEnum|string|null $navigationIcon = 'heroicon-o-camera';

    protected static ?string $navigationLabel = 'Players Trackingg';

    protected static ?string $modelLabel = 'Player Tracking';

    protected static ?string $pluralModelLabel = 'Players Tracking';

    protected static string|\UnitEnum|null $navigationGroup = 'Players Tracking';

    protected static ?int $navigationSort = 2;

    public static function getNavigationLabel(): string
    {
        return 'Captcha List';
    }

    public static function form(Schema $schema): Schema
    {
        return $schema
            ->components([
                Textarea::make('player_nicks')
                    ->label('Player Nicknames')
                    ->helperText('Comma separated nicknames or IDs')
                    ->required()
                    ->columnSpanFull(),
                Checkbox::make('use_ids')
                    ->label('Use Player IDs instead of nicknames')
                    ->default(false),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultPaginationPageOption(20)
            ->paginationPageOptions([20, 30, 50, 100])
            ->query(null)
            ->records(function (
                ?string $sortColumn,
                ?string $sortDirection,
                ?string $search,
                int $page,
                int $recordsPerPage
            ): LengthAwarePaginator {
                return app(PlayerRepository::class)->getPlayersCaptchaTracking(
                    resultSetOptions: [
                        'page' => $page,
                        'perPage' => $recordsPerPage,
                    ],
                    sorting: [
                        'column' => $sortColumn,
                        'direction' => $sortDirection,
                    ]
                );
            })
            ->headerActions([
                Action::make('add_captcha')
                    ->label('Add Captcha')
                    ->icon('heroicon-o-plus')
                    ->color('success')
                    ->url(fn () => static::getUrl('add-captcha')),
            ])
            ->recordUrl(null)
            ->columns([
                TextColumn::make('player_id')
                    ->label('Id')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('nick_name')
                    ->label('Nickname')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('cur_proxy_id')
                    ->label('Status')
                    ->formatStateUsing(function ($state) {
                        return $state == 0 ? 'Offline' : 'Online';
                    })
                    ->badge()
                    ->color(fn ($state) => $state == 0 ? 'danger' : 'success')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('entry_fail')
                    ->label('Entry Failed')
                    ->sortable()
                    ->toggleable(),
                IconColumn::make('verified')
                    ->label('Verified')
                    ->boolean()
                    ->sortable()
                    ->toggleable(),
                IconColumn::make('pending')
                    ->label('Pending')
                    ->boolean()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('date_created')
                    ->label('Date Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('date_verified')
                    ->label('Date Verified')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => CaptchaList::route('/'),
            'add-captcha' => AddCaptcha::route('/add-captcha'),
            'heatmap' => Heatmap::route('/heatmap'),
            'screenshoting' => ScreenShoting::route('/screenshoting'),
            'multi-account-check' => MultiAccountCheck::route('/multi-account-check'),
        ];
    }
}
