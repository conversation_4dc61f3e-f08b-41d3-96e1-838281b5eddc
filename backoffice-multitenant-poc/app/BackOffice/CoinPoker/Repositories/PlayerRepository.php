<?php

namespace App\BackOffice\CoinPoker\Repositories;

use App\BackOffice\CoinPoker\Contracts\PlayerDataSourceInterface;
use Illuminate\Pagination\LengthAwarePaginator;

class PlayerRepository
{
    protected PlayerDataSourceInterface $dataSource;

    public function __construct(PlayerDataSourceInterface $dataSource)
    {
        $this->dataSource = $dataSource;
    }

    public function getPlayers(array $filters = [], array $resultSetOptions = [], array $sorting = [], array $extra = [])
    {
        return $this->dataSource->getPlayers($filters, $resultSetOptions, $sorting, $extra);
    }

    /**
     * Get a single player by player ID or criteria array
     *
     * @param  int|array  $criteria  - Player ID (int) or criteria array ['field' => 'value']
     *                               Examples:
     *                               - getPlayer(123) - find by player_id = 123
     *                               - getPlayer(['id' => 123]) - find by player_id = 123
     *                               - getPlayer(['nickname' => 'john']) - find by nick_name = 'john'
     *                               - getPlayer(['email' => '<EMAIL>']) - find by email = '<EMAIL>'
     */
    public function getPlayer(int|array $criteria): ?array
    {
        return $this->dataSource->getPlayer($criteria);
    }

    public function getPlayersForProviderTable(array $filters = [], int $page = 1, int $perPage = 50, array $sorting = [], ?string $search = null): LengthAwarePaginator
    {
        return $this->dataSource->getPlayersForProviderTable($filters, $page, $perPage, $sorting, $search);
    }

    /**
     * @todo: refactor this to getAccountingApiPlayerInfo
     */
    public function getPlayerInfo(string $playerId): ?array
    {
        return $this->dataSource->getAccountingApiPlayerInfo($playerId);
    }

    public function getRestrictionGroups(array $filters = [], int $page = 1, int $perPage = 15, array $sorting = [])
    {
        return $this->dataSource->getRestrictionGroups($filters, $page, $perPage, $sorting);
    }

    public function getPlayersTracking(array $filters = [], array $resultSetOptions = [], array $sorting = [])
    {
        return $this->dataSource->getPlayersTracking($filters, $resultSetOptions, $sorting);
    }

    public function getPlayersCaptchaTracking(array $filters = [], array $resultSetOptions = [], array $sorting = [])
    {
        return $this->dataSource->getPlayersCaptchaTracking($filters, $resultSetOptions, $sorting);
    }

    public function getPlayersHeatmap(array $filters = [], array $resultSetOptions = [], array $sorting = [])
    {
        return $this->dataSource->getPlayersHeatmap($filters, $resultSetOptions, $sorting);
    }

    public function getMultiAccountData(array $filters = [], int $page = 1, int $recordsPerPage = 20): LengthAwarePaginator
    {
        return $this->dataSource->getMultiAccountData($filters, $page, $recordsPerPage);
    }
}
