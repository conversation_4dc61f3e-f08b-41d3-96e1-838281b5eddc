<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateTestUsers extends Command
{
    protected $signature = 'tenant:create-test-users';
    
    protected $description = 'Create test users with different roles for demonstration purposes';

    public function handle()
    {
        $this->info('Creating test users with different roles...');

        // Create superadmin user
        $superadmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Administrator',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );
        $superadmin->assignRole('superadmin');
        $this->info('✅ Created superadmin: <EMAIL> (password: password123)');

        // Create Coincasino admin
        $coincasinoAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Coincasino Administrator',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'google_workspace_domain' => 'coincasino.dev',
            ]
        );
        $coincasinoAdmin->syncRoles(['admin-coincasino']);
        $this->info('✅ Created Coincasino admin: <EMAIL> (password: password123)');

        // Create Coincasino viewer
        $coincasinoViewer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Coincasino Viewer',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'google_workspace_domain' => 'coincasino.dev',
            ]
        );
        $coincasinoViewer->syncRoles(['viewer-coincasino']);
        $this->info('✅ Created Coincasino viewer: <EMAIL> (password: password123)');

        // Create Coinpoker admin
        $coinpokerAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Coinpoker Administrator',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'google_workspace_domain' => 'coinpoker.dev',
            ]
        );
        $coinpokerAdmin->syncRoles(['admin-coinpoker']);
        $this->info('✅ Created Coinpoker admin: <EMAIL> (password: password123)');

        // Create Coinpoker viewer
        $coinpokerViewer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Coinpoker Viewer',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'google_workspace_domain' => 'coinpoker.dev',
            ]
        );
        $coinpokerViewer->syncRoles(['viewer-coinpoker']);
        $this->info('✅ Created Coinpoker viewer: <EMAIL> (password: password123)');

        $this->newLine();
        $this->info('🎉 Test users created successfully!');
        $this->info('Now you can test the permissions by:');
        $this->info('1. Visiting http://coincasino.dev/admin with different user credentials');
        $this->info('2. Visiting http://coinpoker.dev/admin with different user credentials');
        $this->info('3. Testing with: sail php artisan tenant:test-permissions <email>');

        return 0;
    }
} 