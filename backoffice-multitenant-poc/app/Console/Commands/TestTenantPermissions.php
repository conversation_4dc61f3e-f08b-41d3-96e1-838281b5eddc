<?php

namespace App\Console\Commands;

use App\Models\Tenant;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Role;

class TestTenantPermissions extends Command
{
    protected $signature = 'tenant:test-permissions {email}';
    
    protected $description = 'Test tenant permissions for a specific user';

    public function handle()
    {
        $email = $this->argument('email');
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email {$email} not found.");
            return 1;
        }

        $this->info("Testing permissions for user: {$user->name} ({$user->email})");
        $this->info("User roles: " . $user->roles->pluck('name')->join(', '));

        // Authenticate the user for testing purposes
        Auth::login($user);

        // Test for each tenant
        $tenants = Tenant::all();

        foreach ($tenants as $tenant) {
            $this->newLine();
            $this->info("=== Testing for tenant: {$tenant->name} ({$tenant->domain}) ===");
            
            // Make tenant current
            $tenant->makeCurrent();

            // Test read permission
            $readPermission = match($tenant->domain) {
                'coincasino.dev' => 'coincasino_users_section_read',
                'coinpoker.dev' => 'coinpoker_users_section_read',
                default => null
            };

            // Test write permission
            $writePermission = match($tenant->domain) {
                'coincasino.dev' => 'coincasino_users_section_write',
                'coinpoker.dev' => 'coinpoker_users_section_write',
                default => null
            };

            if ($readPermission) {
                $canRead = $user->can($readPermission);
                $this->line("Can read users: " . ($canRead ? '✅ YES' : '❌ NO'));
            }

            if ($writePermission) {
                $canWrite = $user->can($writePermission);
                $this->line("Can write users: " . ($canWrite ? '✅ YES' : '❌ NO'));
            }

            // Test with our service (now with authenticated user)
            $this->line("TenantPermissionService::canRead(): " . (\App\Services\TenantPermissionService::canRead() ? '✅ YES' : '❌ NO'));
            $this->line("TenantPermissionService::canWrite(): " . (\App\Services\TenantPermissionService::canWrite() ? '✅ YES' : '❌ NO'));
        }

        // Clear authentication
        Auth::logout();

        $this->newLine();
        $this->info("Available roles in the system:");
        Role::all()->each(function ($role) {
            $permissions = $role->permissions->pluck('name')->join(', ');
            $this->line("- {$role->name}: {$permissions}");
        });

        return 0;
    }
} 