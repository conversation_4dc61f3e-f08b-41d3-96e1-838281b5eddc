<?php

namespace App\Console\Commands;

use App\Models\Tenant;
use Illuminate\Console\Command;

class SetupTenantsCommand extends Command
{
    protected $signature = 'tenants:setup';
    
    protected $description = 'Set up the initial tenants for the multi-tenant application';

    public function handle()
    {
        $this->info('Setting up tenants...');

        // Create coinpoker tenant
        $coinpoker = Tenant::updateOrCreate(
            ['domain' => 'coinpoker.dev'],
            [
                'name' => 'CoinPoker',
                'database' => 'coinpoker_db',
            ]
        );

        // Create coincasino tenant
        $coincasino = Tenant::updateOrCreate(
            ['domain' => 'coincasino.dev'],
            [
                'name' => 'CoinCasino',
                'database' => 'coincasino_db',
            ]
        );

        $this->info('✅ CoinPoker tenant created: ' . $coinpoker->domain);
        $this->info('✅ CoinCasino tenant created: ' . $coincasino->domain);

        $this->info('🎉 Tenants setup completed!');
        
        $this->line('');
        $this->line('Next steps:');
        $this->line('1. Add these entries to your /etc/hosts file:');
        $this->line('   127.0.0.1 coinpoker.dev');
        $this->line('   127.0.0.1 coincasino.dev');
        $this->line('');
        $this->line('2. Run migrations for each tenant:');
        $this->line('   php artisan tenant:artisan "migrate" --tenant=' . $coinpoker->id);
        $this->line('   php artisan tenant:artisan "migrate" --tenant=' . $coincasino->id);
        $this->line('');
        $this->line('3. Access your applications:');
        $this->line('   🎰 CoinPoker: http://coinpoker.dev');
        $this->line('   🎲 CoinCasino: http://coincasino.dev');

        return 0;
    }
} 