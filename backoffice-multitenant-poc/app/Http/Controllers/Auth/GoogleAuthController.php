<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Lara<PERSON>\Socialite\Facades\Socialite;

class Google<PERSON>uthController extends Controller
{
    /**
     * Redirect to Google OAuth
     */
    public function redirect(Request $request)
    {
        // Store the intended URL in session
        if ($request->has('intended')) {
            session(['intended_url' => $request->get('intended')]);
        }
        
        return Socialite::driver('google')->redirect();
    }

    /**
     * Handle Google OAuth callback
     */
    public function callback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();
            
            // Check if user is from the required workspace domain
            if (config('services.google.hosted_domain')) {
                $userDomain = $this->extractDomainFromEmail($googleUser->getEmail());
                $requiredDomain = config('services.google.hosted_domain');
                
                if ($userDomain !== $requiredDomain) {
                    return redirect('/admin/login')->withErrors([
                        'email' => 'You must use a ' . $requiredDomain . ' email address to log in.'
                    ]);
                }
            }
            
            // Find or create user
            $user = $this->findOrCreateUser($googleUser);
            
            // Log the user in
            Auth::login($user);
            
            // Get intended URL from session or default to admin dashboard
            $intendedUrl = session('intended_url', '/admin');
            session()->forget('intended_url');
            
            // Redirect to intended page or admin dashboard
            return redirect($intendedUrl);
            
        } catch (\Exception $e) {
            return redirect('/admin/login')->withErrors([
                'email' => 'Google authentication failed. Please try again.'
            ]);
        }
    }

    /**
     * Find or create user based on Google OAuth data
     */
    private function findOrCreateUser($googleUser)
    {
        // First try to find by Google ID
        $user = User::where('google_id', $googleUser->getId())->first();
        
        if ($user) {
            // Update user data if found
            $user->update([
                'name' => $googleUser->getName(),
                'avatar' => $googleUser->getAvatar(),
                'google_workspace_domain' => $this->extractDomainFromEmail($googleUser->getEmail()),
            ]);
            return $user;
        }
        
        // Try to find by email
        $user = User::where('email', $googleUser->getEmail())->first();
        
        if ($user) {
            // Link existing user with Google account
            $user->update([
                'google_id' => $googleUser->getId(),
                'name' => $googleUser->getName(),
                'avatar' => $googleUser->getAvatar(),
                'google_workspace_domain' => $this->extractDomainFromEmail($googleUser->getEmail()),
            ]);
            return $user;
        }
        
        // Create new user
        return User::create([
            'name' => $googleUser->getName(),
            'email' => $googleUser->getEmail(),
            'google_id' => $googleUser->getId(),
            'avatar' => $googleUser->getAvatar(),
            'google_workspace_domain' => $this->extractDomainFromEmail($googleUser->getEmail()),
            'password' => Hash::make(Str::random(16)), // Random password since they'll use Google OAuth
            'email_verified_at' => now(), // Google emails are pre-verified
        ]);
    }

    /**
     * Extract domain from email address
     */
    private function extractDomainFromEmail(string $email): string
    {
        return substr(strrchr($email, "@"), 1);
    }
}
