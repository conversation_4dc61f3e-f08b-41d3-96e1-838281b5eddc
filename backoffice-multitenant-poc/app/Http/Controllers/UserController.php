<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    public function indexCoincasino()
    {
        // Verificar permisos para usuarios de Coincasino
        if (!Auth::user()->can('coincasino_users_section_read')) {
            abort(403, 'No tienes permisos para ver usuarios de Coincasino');
        }

        $users = User::all(); // En un caso real, filtrarías por tenant/domain
        return view('users.coincasino.index', compact('users'));
    }

    public function storeCoincasino(Request $request)
    {
        // Verificar permisos de escritura para usuarios de Coincasino
        if (!Auth::user()->can('coincasino_users_section_write')) {
            abort(403, 'No tienes permisos para crear usuarios de Coincasino');
        }

        // Lógica para crear usuario...
        return response()->json(['message' => 'Usuario creado exitosamente']);
    }

    public function indexCoinpoker()
    {
        // Verificar permisos para usuarios de Coinpoker
        if (!Auth::user()->can('coinpoker_users_section_read')) {
            abort(403, 'No tienes permisos para ver usuarios de Coinpoker');
        }

        $users = User::all(); // En un caso real, filtrarías por tenant/domain
        return view('users.coinpoker.index', compact('users'));
    }

    public function storeCoinpoker(Request $request)
    {
        // Verificar permisos de escritura para usuarios de Coinpoker
        if (!Auth::user()->can('coinpoker_users_section_write')) {
            abort(403, 'No tienes permisos para crear usuarios de Coinpoker');
        }

        // Lógica para crear usuario...
        return response()->json(['message' => 'Usuario creado exitosamente']);
    }

    /**
     * Método de ejemplo usando middleware para verificar permisos
     */
    public function __construct()
    {
        // Ejemplo alternativo usando middleware
        // $this->middleware('permission:coincasino_users_section_read')->only(['indexCoincasino']);
        // $this->middleware('permission:coincasino_users_section_write')->only(['storeCoincasino']);
    }
} 