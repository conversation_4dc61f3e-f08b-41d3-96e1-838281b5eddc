<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        $permissions = [
            'coincasino_users_section_read',
            'coincasino_users_section_write',
            'coinpoker_users_section_read',
            'coinpoker_users_section_write',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Superadmin Role
        Role::firstOrCreate(['name' => 'superadmin']);

        // Admin Coincasino
        $adminCoincasino = Role::firstOrCreate(['name' => 'admin-coincasino']);
        $adminCoincasino->syncPermissions([
            'coincasino_users_section_read',
            'coincasino_users_section_write'
        ]);

        // Viewer Coincasino
        $viewerCoincasino = Role::firstOrCreate(['name' => 'viewer-coincasino']);
        $viewerCoincasino->syncPermissions([
            'coincasino_users_section_read'
        ]);

        // Admin Coinpoker
        $adminCoinpoker = Role::firstOrCreate(['name' => 'admin-coinpoker']);
        $adminCoinpoker->syncPermissions([
            'coinpoker_users_section_read',
            'coinpoker_users_section_write'
        ]);

        // Viewer Coinpoker
        $viewerCoinpoker = Role::firstOrCreate(['name' => 'viewer-coinpoker']);
        $viewerCoinpoker->syncPermissions([
            'coinpoker_users_section_read'
        ]);

        $this->command->info('✅ Roles and permissions created successfully!');
        $this->command->info('📋 Available roles:');
        $this->command->info('  - superadmin (all permissions)');
        $this->command->info('  - admin-coincasino (coincasino read/write)');
        $this->command->info('  - viewer-coincasino (coincasino read only)');
        $this->command->info('  - admin-coinpoker (coinpoker read/write)');
        $this->command->info('  - viewer-coinpoker (coinpoker read only)');
    }
}
