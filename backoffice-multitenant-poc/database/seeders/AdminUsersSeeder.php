<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUsersSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Crear superadmin
        $superadmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);
        $superadmin->assignRole('superadmin');

        // Crear admin de Coincasino
        $adminCoincasino = User::create([
            'name' => 'Admin Coincasino',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);
        $adminCoincasino->assignRole('admin-coincasino');

        // Crear admin de Coinpoker
        $adminCoinpoker = User::create([
            'name' => 'Admin Coinpoker',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);
        $adminCoinpoker->assignRole('admin-coinpoker');
    }
} 