<?php

namespace Database\Seeders;

use App\Models\Tenant;
use Illuminate\Database\Seeder;

class TenantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Tenant::updateOrCreate(
            ['domain' => 'coinpoker.dev'],
            [
                'name' => 'CoinPoker',
                'database' => 'coinpoker_db',
            ]
        );

        Tenant::updateOrCreate(
            ['domain' => 'coincasino.dev'],
            [
                'name' => 'CoinCasino',
                'database' => 'coincasino_db',
            ]
        );

        $this->command->info('✅ Default tenants created: coinpoker.dev, coincasino.dev');
    }
}
