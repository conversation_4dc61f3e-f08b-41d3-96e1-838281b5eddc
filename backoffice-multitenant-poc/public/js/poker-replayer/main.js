const MAX_PLAYERS = 8; //max players + 1 for while cicles

var app = {
    button: 1,
    speed: 500,
    isPaused: 0,
    lines: null,
    hand_part: 0,
    pot: 0,
    min_bet: 0,
    skip_lines: 0,
    game_type: 1,
    game_event_type: 2,
    currency: '',
    decimals: 2,
    cutting_diff: 1,
    flop: 1,
    turn: 1,
    river: 1,
    openCards: 1
}
var poker_log = null;
var line_index = -1;
var prev_li = 0; //additional line index for previous hand function
var players_info = {
    1: {nickname : '', chips: '', bet: 0, payout: 0, action: ''},
    2: {nickname : '', chips: '', bet: 0, payout: 0, action: ''},
    3: {nickname : '', chips: '', bet: 0, payout: 0, action: ''},
    4: {nickname : '', chips: '', bet: 0, payout: 0, action: ''},
    5: {nickname : '', chips: '', bet: 0, payout: 0, action: ''},
    6: {nickname : '', chips: '', bet: 0, payout: 0, action: ''},
    7: {nickname : '', chips: '', bet: 0, payout: 0, action: ''},

    hasAnyBets: function(){
        var i = 1;
        var max_bet = 0;
        while(i<MAX_PLAYERS){
            max_bet += players_info[i]['bet'];
            i++;
        }
        if(max_bet > 0){
            return true;
        } else {
            return false;
        }
    }
};
var button_pos = {
    1 : {x: 554, y: 480},
    2 : {x: 215, y: 470},
    3 : {x: 159, y: 430},
    4 : {x: 150, y: 220},
    5 : {x: 390, y: 142},
    6 : {x: 830, y: 230},
    7 : {x: 810, y: 430}
};
var snapshot_data = {};

$.get(log_url, function(data) {
    app['lines'] = data.split('\n');
    for(i=0; i < app['lines'].length; i++){
        app['lines'][i] = ' '+app['lines'][i];
    }
    $(".log_loading").hide();
    $(".log_loading_overlay").hide();
});

$("#dealer-button").css({
    'top' : button_pos[app['button']]['y'] + 'px',
    'left' : button_pos[app['button']]['x'] + 'px'
});

$("#controls-prev").click(function(){
    if(line_index > 0){
        if(app['isPaused'] == 0){
            if(typeof snapshot_data[line_index-1] === 'undefined'){
               while(typeof snapshot_data[line_index-1] === 'undefined' && line_index >= -1){
                   console.log(line_index);
                   line_index--;
               }

            } else {
                line_index--;
            }
            load_recorded_step();
        }
    }
});

$("#controls-next").click(function(){
    if(app['isPaused'] == 0){
        record_step_info();
        line_index++;
        read_log();
    }
});

$("#controls-start").click(function(){
    poker_log = setInterval(function(){
        if(app['isPaused'] === 0){
            record_step_info();
            line_index++;
            read_log();
        }
    }, app['speed']);

    $("#controls-pause").show();
    $(this).hide();
});

function read_log(){
    if (line_index >= app['lines'].length-1){
        clearInterval(poker_log);
        clear_table();
    } else {
        /* hand part */
        log_hand_part();

        if(app['hand_part'] == 1){
            execute_hand_part_1();
        } else if(app['hand_part'] == 2){
            execute_hand_part_2();
        }
    }
}

$("#controls-pause").click(function(){
    clearInterval(poker_log);
    $("#controls-start").show();
    $(this).hide();
});

$("#controls-stop").click(function(){

    if(app['isPaused'] === 0){
        clearInterval(poker_log);

        line_index = 0;
        load_recorded_step();
        $("#controls-pause").hide();
        $("#controls-start").show();
        $("#table").html(snapshot_data[line_index]['html_data']);
        line_index = -1;
    }
});

$("#controls-prev-hand").click(function(){
    if(line_index > 0){
        $("#controls-pause").click();
        clear_table();
        line_index = prev_li;

        line_index--; //this line_index-- neccessary for while cicle to run
        while(app['lines'][line_index].includes("Hand #") == false){
            line_index--; //cicle runs trough lines to find hand start line
        }
        prev_li = line_index;
        /* hand part */
        read_log();
        line_index++;

        app['button'] = app['lines'][line_index].slice(app['lines'][line_index].search(" is the button")-1, app['lines'][line_index].search(" is the button"));

        if(app['button'] > 7){
            app['button'] = 1;
        } else if(app['button'] <1){
            app['button'] = 7;
        }

        $('#dealer-button').css({
            top: button_pos[app['button']]['y']+'px',
            left: button_pos[app['button']]['x']+'px'});
        line_index++;
        read_log();
    }
});

$("#controls-next-hand").click(function(){
    if(line_index < app['lines'].length-1){
        $("#controls-pause").click();
        clear_table();
        prev_li = line_index;
        line_index++; //this line_index++ neccessary for while cicle to run
        while(app['lines'][line_index].includes("Hand #") == false){
            line_index++; //cicle runs trough lines to find hand start line
        }
        read_log();
        record_step_info();
        line_index++;

        app['button'] = app['lines'][line_index].slice(app['lines'][line_index].search(" is the button")-1, app['lines'][line_index].search(" is the button"));

        if(app['button'] > 6){
            app['button'] = 1;
        } else if(app['button'] <1){
            app['button'] = 6;
        }

        $('#dealer-button').css({
            top: button_pos[app['button']]['y']+'px',
            left: button_pos[app['button']]['x']+'px'});
        record_step_info();
        line_index++;
        read_log();
    } else {
        $("#hand-number").show().html("End of log");
    }
    console.log(snapshot_data.first());
    console.log(line_index);
});

function log_hand_part(){
    if(app['lines'][line_index].includes(" is disconnected") || app['lines'][line_index].includes(" has reconnected") || app['lines'][line_index].includes(" said, ")){
        line_index++;
    }
    if(app['lines'][line_index].includes("Hand #")){
        app['hand_part'] = 1;
        if(app['lines'][line_index].includes(" 5 Card Omaha ")) {
            app['game_type'] = 16;
        } else if(app['lines'][line_index].includes(" Omaha Pot Limit ")){
            app['game_type'] = 2;
        }  else {
            app['game_type'] = 1;
        }
        if(app['lines'][line_index].includes(" Tournament ")){
            app['currency'] = "";
            app['decimals'] = 0;
            app['cutting_diff'] = 0;
        } else {
            app['currency'] = "€";
        }
        set_hand_info();
    } else if (app['lines'][line_index].includes("*** HOLE CARDS ***")) {
        app['hand_part'] = 2;
    } else if (app['lines'][line_index].includes("*** FLOP ***")){
        app['flop'] == 1 ? count_pot(1) : count_pot(0);
        flop();
        app['flop']++;
    } else if (app['lines'][line_index].includes("*** TURN ***")){
        app['turn'] == 1 ? count_pot(1) : count_pot(0);
        turn();
        app['turn']++;
    } else if (app['lines'][line_index].includes("*** RIVER ***")){
        app['river'] == 1 ? count_pot(1) : count_pot(0);
        river();
        app['river']++;
    } else if (app['lines'][line_index].includes("*** SHOW DOWN ***")){
        if(players_info.hasAnyBets()){
            count_pot(1);
        } else {
            count_pot(0);
        }
    } else if (app['lines'][line_index].includes("*** SUMMARY ***")){
        clear_table();
        app['hand_part'] = 3;
        line_index += app['skip_lines'];
    } else if (app['lines'][line_index].includes("*** SERVER ***")){
        clear_table();
        line_index += app['skip_lines'];
    }
}

function execute_hand_part_1(){
    if(app['lines'][line_index].includes(" is the button")){ //button possition
        app['button'] = app['lines'][line_index].slice(app['lines'][line_index].search(" is the button")-1, app['lines'][line_index].search(" is the button"));
        button_possition();
    } else if(app['lines'][line_index].includes("Seat ")){ //players info
        app['skip_lines'] = 1;

        var i = 1;
        while(i < MAX_PLAYERS){
            $("#player-"+i+"-info").hide();
            i++;
        }

        while(app['lines'][line_index].includes("Seat ")){
            var seat = app['lines'][line_index].slice(6, 7);
            if(app['lines'][line_index].includes(" out of hand")){
                $("#player-"+seat+"-info").hide();
            } else {
                players_info[seat]["nickname"] = app['lines'][line_index].slice(app['lines'][line_index].search(": ")+2, app['lines'][line_index].search(" \\("));

                var chips = app['lines'][line_index].slice(app['lines'][line_index].search(" \\(")+2, app['lines'][line_index].search(" in"));
                app['skip_lines']++;
                players_info[seat]["chips"] = format_currency(chips);
                set_player_info(seat);
            }
            if(app['lines'][line_index+1].includes(" posts ")){break;}
            line_index++;
        }
    } else if(app['lines'][line_index].includes("posts ")){
        if(app['lines'][line_index].includes(" the ante ")){
            ante_bet();
        } else if(app['lines'][line_index].includes(" straddle ")){
            straddle_bet();
        } else {
            blind_bet();
        }
    }
}

function execute_hand_part_2(){
    var move_player = app['lines'][line_index].slice(1, app['lines'][line_index].search(": "));
    if(app['lines'][line_index].includes(" folds")){
        fold_bet(move_player);
    } else if(app['lines'][line_index].includes(": checks")){
        check(move_player);
    } else if(app['lines'][line_index].includes(" calls ")){
        call_bet(move_player);
    } else if(app['lines'][line_index].includes(" raises ")){
        raise_bet(move_player);
    }else if(app['lines'][line_index].includes(": bets ")){
        place_bet(move_player);
    } else if(app['lines'][line_index].includes(" returned ")){
        return_bet();
    } else if(app['lines'][line_index].includes(": shows ")){
        show_down(move_player);
    } else if(app['lines'][line_index].includes(" collected ")){
        var collect_move_player = app['lines'][line_index].slice(1, app['lines'][line_index].search(" collected "));
        if(app['pot'] == 0){
           instant_win(collect_move_player);
        } else {
            pay_out(collect_move_player);
        }
    }
}

function set_hand_info(){
    var hand_number = app['lines'][line_index].slice(app['lines'][line_index].search("Hand ")+5, app['lines'][line_index].search(": "));
    $("#hand-number").show().html(hand_number);
}

function button_possition() {
    if(app['button'] > 7){
        app['button'] = 1;
    } else if(app['button'] <1){
        app['button'] = 7;
    }

    app['isPaused'] = 1;
    dealer_button();
}

function set_player_info(seat) {
    $("#player-"+seat+"-info > #player-nickname").html(players_info[seat]["nickname"]);
    $("#player-"+seat+"-info > #player-action").html(app['currency']+players_info[seat]["chips"].toFixed(app['decimals']));
    $("#player-"+seat+"-info").show();
    if(app['openCards'] == 0){
        $(".player-"+seat+"-cards").append(
            '<div class="r-card card-closed"></div><div class="r-card card-closed"></div>'
        );
        if(app['game_type'] == 2){
            $(".player-"+seat+"-cards").append(
                '<div class="r-card card-closed"></div><div class="r-card card-closed"></div>'
            );
        }
        if(app['game_type'] == 16) {
            $(".player-"+seat+"-cards").append(
                '<div class="r-card card-closed"></div><div class="r-card card-closed"></div><div class="r-card card-closed"></div>'
            );
        }
    } else {
        set_open_cards(seat);
    }
}

function set_open_cards(seat){
    var li = line_index;
    while(!app['lines'][li].includes("*** SERVER ***")){
          li++;
    }
    while(!app['lines'][li].includes(seat-1+": pid")){
        li++;
        if(app['lines'][li].includes("Hand #")){
            break;
        }
    }

    if(app['lines'][li].includes(seat-1+": pid")){
        var cards_block = app['lines'][li].slice(app['lines'][li].search(" cards=")+8, app['lines'][li].length-1);

        if(cards_block){
            $(".player-"+seat+"-cards").append(
                '<div class="r-card card-'+cards_block.slice(0, 2)+'"></div><div class="r-card card-'+cards_block.slice(2,4)+'"></div>'
            );
            if(app['game_type'] == 2) {
                $(".player-"+seat+"-cards").append(
                    '<div class="r-card card-'+cards_block.slice(4, 6)+'"></div><div class="r-card card-'+cards_block.slice(6, 8)+'"></div>'
                );
            }
            if(app['game_type'] == 16){
                $(".player-"+seat+"-cards").append(
                    '<div class="r-card card-'+cards_block.slice(4, 6)+'"></div><div class="r-card card-'+cards_block.slice(6, 8)+'"></div><div class="r-card card-'+cards_block.slice(8, 10)+'"></div>'
                );
            }
        }
    } else {
        $(".player-"+seat+"-cards").append(
            '<div class="r-card card-closed"></div><div class="r-card card-closed"></div>'
        );
        if(app['game_type'] == 2){
            $(".player-"+seat+"-cards").append(
                '<div class="r-card card-closed"></div><div class="r-card card-closed"></div>'
            );
        }
        if(app['game_type'] == 2){
            $(".player-"+seat+"-cards").append(
                '<div class="r-card card-closed"></div><div class="r-card card-closed"></div><div class="r-card card-closed"></div>'
            );
        }
    }
}

function blind_bet(){
    var blind_player = app['lines'][line_index].slice(1, app['lines'][line_index].search(": "));
    var blind = app['lines'][line_index].slice(app['lines'][line_index].search(" blind")+7, app['lines'][line_index].length);
    var i = 1;
    while(i < MAX_PLAYERS){
        if(players_info[i]['nickname'] == blind_player){
            if(app['lines'][line_index].includes(" small blind ")){
                players_info[i]['action'] = 'Small Blind';
            } else {
                players_info[i]['action'] = 'Big Blind';
            }
            $("#player-"+i+"-info > #player-action").html('<span style="color: #ffc000">'+players_info[i]['action']+'</span>');
            players_info[i]['chips'] -= format_currency(blind);
            count_bet(i, format_currency(blind));
        }
        i++;
    }
}

function ante_bet(){
    var ante_player = app['lines'][line_index].slice(1, app['lines'][line_index].search(": "));
    var ante = app['lines'][line_index].slice(app['lines'][line_index].search(" the ante")+10, app['lines'][line_index].length);
    var i = 1;
    while(i < MAX_PLAYERS){
        if(players_info[i]['nickname'] == ante_player){
            players_info[i]['action'] = 'Ante';
            $("#player-"+i+"-info > #player-action").html('<span style="color: #ffc000">'+players_info[i]['action']+'</span>');
            players_info[i]['chips'] -= format_currency(ante);
            count_bet(i, format_currency(ante));
        }
        i++;
    }
}

function straddle_bet(){
    var straddle_player = app['lines'][line_index].slice(1, app['lines'][line_index].search(": "));
    var straddle = app['lines'][line_index].slice(app['lines'][line_index].search(" straddle")+10, app['lines'][line_index].length);
    var i = 1;
    while(i < MAX_PLAYERS){
        if(players_info[i]['nickname'] == straddle_player){
            players_info[i]['action'] = 'Straddle';
            $("#player-"+i+"-info > #player-action").html('<span style="color: #ffc000">'+players_info[i]['action']+'</span>');
            players_info[i]['chips'] -= format_currency(straddle);
            count_bet(i, format_currency(straddle));
        }
        i++;
    }
}

function fold_bet(player){
    var i = 1;
    while(i < MAX_PLAYERS){
        if(players_info[i]['nickname'] == player){
            players_info[i]['action'] = 'Fold';
            remove_cards(i);
            $("#player-"+i+"-info > #player-action").html('<span style="color: #ffc000">'+players_info[i]['action']+'</span>');
        }
        i++;
    }
}

function check(player){
    var i = 1;
    while(i < MAX_PLAYERS){
        if(players_info[i]['nickname'] == player){
            players_info[i]['action'] = 'Check';
            $("#player-"+i+"-info > #player-action").html('<span style="color: #ffc000">'+players_info[i]['action']+'</span>');
        }
        i++;
    }
}

function call_bet(player){
    var amount = null;
    var i = 1;
    if(app['lines'][line_index].includes(" all-in")){
        amount = app['lines'][line_index].slice(app['lines'][line_index].search(" calls")+7, app['lines'][line_index].search(" and is "));
    } else {
        amount = app['lines'][line_index].slice(app['lines'][line_index].search(" calls")+7, app['lines'][line_index].length);
    }
    while(i < MAX_PLAYERS){
        if(players_info[i]['nickname'] == player){
            players_info[i]['action'] = 'Call';
            $("#player-"+i+"-info > #player-action").html('<span style="color: #ffc000">'+players_info[i]['action']+'</span>');
            players_info[i]['chips'] -= format_currency(amount);
            count_bet(i, format_currency(amount));
        }
        i++;
    }
}

function raise_bet(player){
    var amount = null;
    var i = 1;
    if(app['lines'][line_index].includes(" all-in")){
        amount = app['lines'][line_index].slice(app['lines'][line_index].search(" raises ")+8, app['lines'][line_index].search(" and is "));

        while(i < MAX_PLAYERS){
            if(players_info[i]['nickname'] == player){
                players_info[i]['action'] = 'Raise';
                $("#player-"+i+"-info > #player-action").html('<span style="color: #ffc000">'+players_info[i]['action']+'</span>');
                players_info[i]['chips'] -= format_currency(amount);
                count_bet(i, format_currency(amount));
            }
            i++;
        }
    } else {
        while(i < MAX_PLAYERS){
            if(players_info[i]['nickname'] == player){
                amount = app['lines'][line_index].slice(app['lines'][line_index].search(" to ")+4, app['lines'][line_index].length);
                players_info[i]['action'] = 'Raise';
                $("#player-"+i+"-info > #player-action").html('<span style="color: #ffc000">'+players_info[i]['action']+'</span>');
                players_info[i]['chips'] -= (format_currency(amount) - players_info[i]['bet']);
                players_info[i]['bet'] = format_currency(amount);
                $("#player-"+i+"-chips").show().html(app['currency']+players_info[i]['bet'].toFixed(app['decimals']));
            }
            i++;
        }
    }
}

function place_bet(player){
    var amount = null;
    if(app['lines'][line_index].includes(" and ")){
        amount = app['lines'][line_index].slice(app['lines'][line_index].search(": bets ")+7, app['lines'][line_index].search(" and "));
    } else {
        amount = app['lines'][line_index].slice(app['lines'][line_index].search(": bets ")+7, app['lines'][line_index].length);
    }
    var i = 1;
    while(i < MAX_PLAYERS){
        if(players_info[i]['nickname'] == player){
            players_info[i]['action'] = 'Bet';
            $("#player-"+i+"-info > #player-action").html('<span style="color: #ffc000">'+players_info[i]['action']+'</span>');
            players_info[i]['chips'] -= format_currency(amount);
            count_bet(i, format_currency(amount));
        }
        i++;
    }
}

function return_bet(){
    var player = app['lines'][line_index].slice(app['lines'][line_index].search(" to ")+4, app['lines'][line_index].length);
    var amount = app['lines'][line_index].slice(15, app['lines'][line_index].search(" returned ")-1);
    var i = 1;
    while(i < MAX_PLAYERS){
        if(players_info[i]['nickname'] == player){
            players_info[i]['bet'] -= format_currency(amount);
            players_info[i]['chips'] += format_currency(amount);
            $("#player-"+i+"-info > #player-action").html(app['currency']+players_info[i]['chips'].toFixed(app['decimals']));
            if(players_info[i]['bet'] != 0){
                $("#player-"+i+"-chips").show().html(app['currency']+players_info[i]['bet'].toFixed(app['decimals']));
            } else {
                $("#player-"+i+"-chips").hide();
            }
        }
        i++;
    }
}

function count_bet(seat, amount){
    players_info[seat]['bet'] += amount;
    $("#player-"+seat+"-chips").show().html(app['currency']+players_info[seat]['bet'].toFixed(app['decimals']));
}

function remove_cards(seat){
    $(".player-"+seat+"-cards").children().remove();
}

function count_pot(action){ //action == 1 for count_pot with animation, action == 2 for count_pot with multiple animations, action == 0 for count_pot w/o animations
    var i = 1;

    if(action == 1){
        app['isPaused'] = 1;
        chips_to_pot(0);
    } else if(action == 2) {
        app['isPaused'] = 1;
        chips_to_pot(1);
    } else {
        while(i < MAX_PLAYERS){
            app['pot'] += players_info[i]['bet'];
            players_info[i]['bet'] = 0;
            if(typeof players_info[i]['chips'] === 'number'){
                if(players_info[i]['chips'].toFixed(app['decimals']) != 0){
                    $("#player-"+i+"-info > #player-action").html(app['currency']+players_info[i]['chips'].toFixed(app['decimals']));
                } else {
                    players_info[i]['action'] = 'all-in';
                    $("#player-"+i+"-info > #player-action").html(players_info[i]['action']);
                }
            }
            i++;
        }
        $("#pot").show().html(app['currency']+app['pot'].toFixed(app['decimals']));
    }
}

function flop(){
    var first_card = app['lines'][line_index].slice(15, 17);
    var second_card = app['lines'][line_index].slice(18, 20);
    var third_card = app['lines'][line_index].slice(21, 23);
    if(app['flop'] == 1){
        $(".table-cards-flop").append(
            '<div class="r-card card-'+first_card+'"></div><div class="r-card card-'+second_card+'"></div><div class="r-card card-'+third_card+'"></div>'
        );
    } else {
        $(".table-cards-flop").css({'top' : 241+'px'});
        $(".table-cards-turn").css({'top' : 241+'px'});
        $(".table-cards-river").css({'top' : 241+'px'});
        $("#pot").css({'top' : 360+'px'});
        $(".table-cards-flop-2").append(
            '<div class="r-card card-'+first_card+'"></div><div class="r-card card-'+second_card+'"></div><div class="r-card card-'+third_card+'"></div>'
        );
    }
}

function turn(){
    var fourth_card = app['lines'][line_index].slice(26, 28);
    if(app['turn'] == 1){
        $(".table-cards-turn").append(
            '<div class="r-card card-'+fourth_card+'"></div>'
        );
    } else {
        $(".table-cards-turn").css({'top' : 241+'px'});
        $(".table-cards-river").css({'top' : 241+'px'});
        $(".table-cards-turn-2").append(
            '<div class="r-card card-'+fourth_card+'"></div>'
        );
    }

}

function river(){
    var fifth_card = app['lines'][line_index].slice(30, 32);
    if(app['river'] == 1){
        $(".table-cards-river").append(
            '<div class="r-card card-'+fifth_card+'"></div>'
        );
    } else {
        $(".table-cards-river").css({'top' : 241+'px'});
        $(".table-cards-river-2").append(
            '<div class="r-card card-'+fifth_card+'"></div>'
        );
    }
}

function show_down(player){
    var player_cards = app['lines'][line_index].slice(app['lines'][line_index].search(" \\[")+2, app['lines'][line_index].search("] "));
    player_cards = player_cards.split(" ");
    var i = 1;
    while(i < MAX_PLAYERS){
        if(players_info[i]['nickname'] == player){
            $(".player-"+i+"-cards").children().remove();
            player_cards.forEach(function(card){
                $(".player-"+i+"-cards").append(
                    '<div class="r-card card-'+card+'"></div>'
                );
            });
        }
        i++;
    }
}

function instant_win(player){
    var i = 1;
    while(i<MAX_PLAYERS){
        if(players_info[i]['nickname'] == player){
            app['isPaused'] = 1;
            instant_win_animation(i);
        }
        i++;
    }
}

function pay_out(player){
    var i = 1;

    if(players_info.hasAnyBets()){
        count_pot(2);
    } else {
        count_pot(0);
    }

    i = 1;

    while(i<MAX_PLAYERS){
        if(players_info[i]['nickname'] == player){
            app['isPaused'] = 1;
            pot_to_chips(i);
        }
        i++;
    }
}

function clear_table(){
    var i = 1;
    min_bet = 0;
    app['pot'] = 0;
    app['flop'] = 1;
    app['turn'] = 1;
    app['river'] = 1;
    $(".table-cards-flop").css({'top' : 271+'px'});
    $(".table-cards-turn").css({'top' : 271+'px'});
    $(".table-cards-river").css({'top' : 271+'px'});
    $(".r-card").remove();
    $('#pot').hide();
    while(i < MAX_PLAYERS){
        players_info[i]['payout'] = 0;
        players_info[i]['bet'] = 0;
        $("#player-"+i+"-chips").hide();
        i++;
    }
}

function format_currency(amount){
    return Number(amount.slice(app['cutting_diff'], amount.length));
}


// STEP RECORDING FOR PREVIOUS BUTTON STARTS HERE

function record_step_info(){
    if(typeof snapshot_data[line_index] === 'undefined'){
        //create deep copy of current log data for previous step
        snapshot_data[line_index] = {
            app: jQuery.extend(true, {}, app),
            players_info: jQuery.extend(true, {}, players_info),
            html_data: $("#table").html()
        }
    }
}

function load_recorded_step(){
    //set step data (objects app and players_info) with deep copy of recorded log step
    app =  jQuery.extend(true, {}, snapshot_data[line_index]['app']);
    players_info = jQuery.extend(true, {}, snapshot_data[line_index]['players_info']);
    players_info['hasAnyBets'] = function(){
        var i = 1;
        var max_bet = 0;
        while(i<MAX_PLAYERS){
            max_bet += players_info[i]['bet'];
            i++;
        }
        if(max_bet > 0){
            return true;
        } else {
            return false;
        }
    };
    $("#table").html(snapshot_data[line_index]['html_data']);
}
