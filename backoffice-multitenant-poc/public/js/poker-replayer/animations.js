var player_chips_pos = {
    1 : {x: 647, y: 405},
    2 : {x: 325, y: 405},
    3 : {x: 250, y: 370},
    4 : {x: 280, y: 207},
    5 : {x: 490, y: 137},
    6 : {x: 753, y: 206},
    7 : {x: 753, y: 360}
};

function dealer_button(){
    var dealerButton = anime({
        targets: '#dealer-button',
        top: button_pos[app['button']]['y']+'px',
        left: button_pos[app['button']]['x']+'px',
        duration: app['speed'],
        easing: 'easeInOutQuad',
        complete: function() {
            app['isPaused'] = 0;
        }
    });
}

function chips_to_pot(animChain){ //animChain == 1 if there will be second animation required after this one
    var chipsToPot = anime({
            targets: ["#player-1-chips", "#player-2-chips", "#player-3-chips", "#player-4-chips", "#player-5-chips", "#player-6-chips",  "#player-7-chips"],
            top: '345px',
            left: '480px',
            duration: app['speed'],
            easing: 'easeInOutQuad',
            complete: function() {
                $("#player-1-chips").hide().css({top: player_chips_pos[1]['y']+'px', left: player_chips_pos[1]['x']+'px'});
                $("#player-2-chips").hide().css({top: player_chips_pos[2]['y']+'px', left: player_chips_pos[2]['x']+'px'});
                $("#player-3-chips").hide().css({top: player_chips_pos[3]['y']+'px', left: player_chips_pos[3]['x']+'px'});
                $("#player-4-chips").hide().css({top: player_chips_pos[4]['y']+'px', left: player_chips_pos[4]['x']+'px'});
                $("#player-5-chips").hide().css({top: player_chips_pos[5]['y']+'px', left: player_chips_pos[5]['x']+'px'});
                $("#player-6-chips").hide().css({top: player_chips_pos[6]['y']+'px', left: player_chips_pos[6]['x']+'px'});
                $("#player-7-chips").hide().css({top: player_chips_pos[7]['y']+'px', left: player_chips_pos[7]['x']+'px'});

                var i = 1;
                
                while(i < MAX_PLAYERS){
                    app['pot'] += players_info[i]['bet'];
                    players_info[i]['bet'] = 0;
                    $("#player-"+i+"-chips").hide();
                    if(typeof players_info[i]['chips'] === 'number'){
                        if(players_info[i]['chips'].toFixed(app['decimals']) != 0){
                            $("#player-"+i+"-info > #player-action").html(app['currency']+players_info[i]['chips'].toFixed(app['decimals']));
                        } else {
                            players_info[i]['action'] = 'all-in';
                            $("#player-"+i+"-info > #player-action").html(players_info[i]['action']);
                        }
                    }
                    i++;
                }
                $("#pot").show().html(app['currency']+app['pot'].toFixed(app['decimals']));
                app['isPaused'] = animChain;
            }
        });
}

function pot_to_chips(seat){
    var amount = app['lines'][line_index].slice(app['lines'][line_index].search(" collected ")+11, app['lines'][line_index].search(" from "));
    var potToChips = anime({
            targets: ["#won-pot"],
            top: player_chips_pos[seat]['y']+'px',
            left: player_chips_pos[seat]['x']+'px',
            delay: app['speed']*1.5,
            duration: app['speed'],
            easing: 'easeInOutQuad',
            begin: function(){
                if(app['lines'][line_index].includes(" from pot")){
                    players_info[seat]['action'] = 'Collected from pot';
                    $("#player-"+seat+"-info > #player-action").html('<span style="color: #ffc000; font-size: 12px;">'+players_info[seat]['action']+'</span>');  
                } else {
                    players_info[seat]['action'] = 'Collected from side-pot';
                    $("#player-"+seat+"-info > #player-action").html('<span style="color: #ffc000; font-size: 12px;">'+players_info[seat]['action']+'</span>');  
                }
                players_info[seat]['payout'] += format_currency(amount);
                $("#won-pot").show().html(app['currency']+players_info[seat]['payout'].toFixed(app['decimals']));
                
                app['pot'] -= format_currency(amount);
                if(app['lines'][line_index+1].includes("*** SUMMARY ***") || app['lines'][line_index+1].includes(" doesn't show ")){
                    $("#pot").hide();
                } else {
                    $("#pot").show().html(app['currency']+app['pot'].toFixed(app['decimals']));
                }
            },
            complete: function() {
                $("#won-pot").hide().css({top: '345px', left: '480px'});
                
                $("#player-"+seat+"-chips").show().html(app['currency']+players_info[seat]['payout'].toFixed(app['decimals']));
                
                app['isPaused'] = 0;
            }
        });
}

function instant_win_animation(seat){
    var amount = app['lines'][line_index].slice(app['lines'][line_index].search(" collected ")+11, app['lines'][line_index].search(" from "));

    var intsWinAnim = anime({
            targets: ["#player-1-chips", "#player-2-chips", "#player-3-chips", "#player-4-chips", "#player-5-chips", "#player-6-chips", "#player-7-chips"],
            top: player_chips_pos[seat]['y']+'px',
            left: player_chips_pos[seat]['x']+'px',
            duration: app['speed'],
            easing: 'easeInOutQuad',
            complete: function() {
                var i = 1;
                
                while(i < MAX_PLAYERS){
                    app['pot'] += players_info[i]['bet'];
                    players_info[i]['bet'] = 0;
                    if(i != seat){
                        $("#player-"+i+"-chips").hide().css({top: player_chips_pos[i]['x']+'px', left: player_chips_pos[i]['y']+'px'});
                    }
                    i++;
                }
                i = 1;
    
                players_info[seat]['payout'] += format_currency(amount);
                players_info[seat]['action'] = 'Collected from pot';
                $("#player-"+seat+"-info > #player-action").html('<span style="color: #ffc000; font-size: 12px;">'+players_info[seat]['action']+'</span>');
                $("#player-"+seat+"-chips").show().html(app['currency']+players_info[seat]['payout'].toFixed(app['decimals']));
                
                app['isPaused'] = 0;
            }
        });
}


























