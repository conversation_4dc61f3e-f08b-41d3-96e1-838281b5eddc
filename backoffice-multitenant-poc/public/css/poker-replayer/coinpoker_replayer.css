#table {
    width: 1005px;
    height: 775px;
    position: relative;
    background-image: url("/js/poker-replayer/assets/images/coinpoker_table.png");
    background-position: center -10px;
}

#table p{
    margin: 16px 0;
}

.log_loading {
    position: absolute;
    z-index: 999;
    height: 2em;
    width: 2em;
    overflow: show;
    margin: auto;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

.log_loading_overlay {
    content: '';
    display: block;
    position: relative;
    width: 1005px;
    height: 775px;
    background-color: rgba(255,255,255,0.3);
    z-index: 998;
}

#hand-number {
    display: none;
    position: absolute;
    top: 690px;
    left: 890px;
    text-align: center;
    font-weight: bold;
    color: white;
    font-size: 16px;
}

.controls>button:focus {outline:0 !important;}
.controls>button>img {margin: auto; display: inline;}


#controls-prev {
    position: absolute;
    left: 624px;
    top: 652px;
    width: 52px;
    height: 43px;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
    background-color: #212121;
    border: 2px solid #a9a9a9;
}

#controls-start {
    position: absolute;
    left: 674px;
    top: 643px;
    width: 66px;
    height: 61px;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    background-color: #212121;
    border: 2px solid #a9a9a9;
}

#controls-pause {
    position: absolute;
    left: 674px;
    top: 643px;
    width: 66px;
    height: 61px;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    background-color: #212121;
    border-style: none solid none none;
    border: 2px solid #a9a9a9;
}

#controls-stop {
    position: absolute;
    left: 747px;
    top: 643px;
    width: 66px;
    height: 61px;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    background-color: #212121;
    margin-left: -4px;
    border: 2px solid #a9a9a9;
}

#controls-next {
    position: absolute;
    left: 803px;
    top: 652px;
    width: 52px;
    height: 43px;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
    background-color: #212121;
    border: 2px solid #a9a9a9;
/*    margin-left: 2px;*/
}


#controls-prev-hand{
    position: absolute;
    top: 651px;
    left: 872px;
    width: 50px;
    height: 43px;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
    background-color: #212121;
    border: 2px solid #a9a9a9;
}

#controls-next-hand{
    position: absolute;
    top: 651px;
    left: 929px;
    width: 50px;
    height: 43px;
    border-top-right-radius: 20px;
    border-bottom-right-radius: 20px;
    background-color: #212121;
    margin-left: -4px;
    border: 2px solid #a9a9a9;
}

#controls-prev:active, #controls-start:active, #controls-pause:active, #controls-stop:active, #controls-next:active, #controls-prev-hand:active, #controls-prev-hand:active, #controls-next-hand:active {
    background-color: #d7d7d7;
}

#controls-prev:focus, #controls-start:focus, #controls-pause:focus, #controls-stop:focus, #controls-next:focus, #controls-prev-hand:focus, #controls-prev-hand:focus, #controls-next-hand:focus {
    box-shadow: none;
}

#dealer-button {
    width: 30px;
    height: 30px;
    position: absolute;
    background-color: #2b2b31;
    border-radius: 50%;
    margin: auto;
}
#dealer-button>img {
    margin: 8px 0px 0px 10px;}

#pot {
    display: none;
    width: 54px;
    height: 20px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.3);
    position: absolute;
    top: 345px;
    left: 480px;
    text-align: center;
    font-weight: bold;
    color: white;
}

.chips {
    width: 45px;
    height: 20px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.3);
    position: absolute;
    text-align: center;
    font-weight: bold;
    font-size: 13px;
    color: white;
    line-height: 20px;
}

#won-pot {
    display: none;
    top: 345px;
    left: 480px;
}

.table-cards-flop {
    display: inline-block;
    position: absolute;
    left: 395px;
    top: 271px;
}

.table-cards-turn {
    display: inline-block;
    position: absolute;
    left: 533px;
    top: 271px;
}

.table-cards-river {
    display: inline-block;
    position: absolute;
    left: 579px;
    top: 271px;
}

.table-cards-flop-2 {
    display: inline-block;
    position: absolute;
    left: 395px;
    top: 303px;
}

.table-cards-turn-2 {
    display: inline-block;
    position: absolute;
    left: 533px;
    top: 303px;
}

.table-cards-river-2 {
    display: inline-block;
    position: absolute;
    left: 579px;
    top: 303px;
}

#player-1-info {
    position: absolute;
    left: 647px;
    top: 495px;
    color: white;
    display: none;
}

#player-2-info {
    position: absolute;
    left: 325px;
    top: 495px;
    color: white;
    display: none;
}

#player-3-info {
    position: absolute;
    left: 100px;
    top: 365px;
    color: white;
    display: none;
}

#player-4-info {
    position: absolute;
    left: 120px;
    top: 157px;
    color: white;
    display: none;
}

#player-5-info {
    position: absolute;
    left: 480px;
    top: 80px;
    color: white;
    display: none;
}

#player-6-info {
    position: absolute;
    left: 870px;
    top: 158px;
    color: white;
    display: none;
}

#player-7-info {
    position: absolute;
    left: 880px;
    top: 365px;
    color: white;
    display: none;
}

#player-nickname {
    margin-bottom: 0px;
}

#player-action {
    margin-top: -13px !important;
    font-size: 12px;
}

#player-1-chips {
    display: none;
    top: 405px;
    left: 647px;
}

#player-2-chips {
    display: none;
    top: 405px;
    left: 325px;
}

#player-3-chips {
    display: none;
    top: 330px;
    left: 250px;
}

#player-4-chips {
    display: none;
    top: 207px;
    left: 280px;
}

#player-5-chips {
    display: none;
    top: 137px;
    left: 490px;
}

#player-6-chips {
    display: none;
    top: 206px;
    left: 753px;
}

#player-7-chips {
    display: none;
    top: 350px;
    left: 753px;
}

.player-1-cards {
    display: inline-block;
    position: absolute;
    left: 600px;
    top: 445px;
    -ms-transform: scale(0.8,0.8); /* IE 9 */
    -webkit-transform: scale(0.8,0.8); /* Safari */
    transform:scale(0.8,0.8);    
    width: 300px;
}

.player-2-cards {
    display: inline-block;
    position: absolute;
    left: 280px;
    top: 445px;
    -ms-transform: scale(0.8,0.8); /* IE 9 */
    -webkit-transform: scale(0.8,0.8); /* Safari */
    transform:scale(0.8,0.8);  
    width: 300px;  
}

.player-3-cards {
    display: inline-block;
    position: absolute;
    left: 55px;
    top: 315px;
    -ms-transform: scale(0.8,0.8); /* IE 9 */
    -webkit-transform: scale(0.8,0.8); /* Safari */
    transform:scale(0.8,0.8);
    width: 300px;
}

.player-4-cards {
    display: inline-block;
    position: absolute;
    left: 80px;
    top: 106px;
    -ms-transform: scale(0.8,0.8); /* IE 9 */
    -webkit-transform: scale(0.8,0.8); /* Safari */
    transform:scale(0.8,0.8);    
    width: 300px;
}

.player-5-cards {
    display: inline-block;
    position: absolute;
    left: 440px;
    top: 33px;
    -ms-transform: scale(0.8,0.8); /* IE 9 */
    -webkit-transform: scale(0.8,0.8); /* Safari */
    transform:scale(0.8,0.8);
    width: 300px;
}

.player-6-cards {
    display: inline-block;
    position: absolute;
    left: 830px;
    top: 108px;
    -ms-transform: scale(0.8,0.8); /* IE 9 */
    -webkit-transform: scale(0.8,0.8); /* Safari */
    transform:scale(0.8,0.8);
    width: 300px;
}

.player-7-cards {
    display: inline-block;
    position: absolute;
    left: 840px;
    top: 313px;
    -ms-transform: scale(0.8,0.8); /* IE 9 */
    -webkit-transform: scale(0.8,0.8); /* Safari */
    transform:scale(0.8,0.8);
    width: 300px;
}

.r-card {
    background-image: url('/js/poker-replayer/assets/images/3.png');
    background-repeat: no-repeat;
    background-position: 0 0;
    display: inline-block;
    position: relative;
    width: 44px;
    height: 60px;
    margin: 1px;
    float: left;
}

.card-closed { background-image: url('/js/poker-replayer/assets/images/cp-card-closed.png'); }
.card-2s { background-position: -0px -0px; }
.card-3s { background-position: -45px -0px; }
.card-4s { background-position: -90px -0px; }
.card-5s { background-position: -135px -0px; }
.card-6s { background-position: -180px -0px; }
.card-7s { background-position: -225px -0px; }
.card-8s { background-position: -270px -0px; }
.card-9s { background-position: -315px -0px; }
.card-Ts { background-position: -360px -0px; }
.card-Js { background-position: -405px -0px; }
.card-Qs { background-position: -450px -0px; }
.card-Ks { background-position: -495px -0px; }
.card-As { background-position: -540px -0px; }
.card-2h { background-position: -0px -61px; }
.card-3h { background-position: -45px -61px; }
.card-4h { background-position: -90px -61px; }
.card-5h { background-position: -135px -61px; }
.card-6h { background-position: -180px -61px; }
.card-7h { background-position: -225px -61px; }
.card-8h { background-position: -270px -61px; }
.card-9h { background-position: -315px -61px; }
.card-Th { background-position: -360px -61px; }
.card-Jh { background-position: -405px -61px; }
.card-Qh { background-position: -450px -61px; }
.card-Kh { background-position: -495px -61px; }
.card-Ah { background-position: -540px -61px; }
.card-2c { background-position: -0px -122px; }
.card-3c { background-position: -45px -122px; }
.card-4c { background-position: -90px -122px; }
.card-5c { background-position: -135px -122px; }
.card-6c { background-position: -180px -122px; }
.card-7c { background-position: -225px -122px; }
.card-8c { background-position: -270px -122px; }
.card-9c { background-position: -315px -122px; }
.card-Tc { background-position: -360px -122px; }
.card-Jc { background-position: -405px -122px; }
.card-Qc { background-position: -450px -122px; }
.card-Kc { background-position: -495px -122px; }
.card-Ac { background-position: -540px -122px; }
.card-2d { background-position: -0px -183px; }
.card-3d { background-position: -45px -183px; }
.card-4d { background-position: -90px -183px; }
.card-5d { background-position: -135px -183px; }
.card-6d { background-position: -180px -183px; }
.card-7d { background-position: -225px -183px; }
.card-8d { background-position: -270px -183px; }
.card-9d { background-position: -315px -183px; }
.card-Td { background-position: -360px -183px; }
.card-Jd { background-position: -405px -183px; }
.card-Qd { background-position: -450px -183px; }
.card-Kd { background-position: -495px -183px; }
.card-Ad { background-position: -540px -183px; }


