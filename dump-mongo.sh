#!/bin/bash

YESTERDAY=$(date -d "yesterday" +%Y-%m-%dT00:00:00Z)
TODAY=$(date -d "today" +%Y-%m-%dT00:00:00Z)
YESTERDAY_DATE=$(date -d "yesterday" +%Y-%m-%d)

CMD=$(echo mongoexport \
  --db=poker \
  --username=history_module \
  --password=JTpxmZJf66Tt \
  --collection=hands \
  --query=\'{ \"tour_id\": 0, \"created_at\": { \"\$gt\": { \"\$date\": \"${YESTERDAY}\" }, \"\$lt\": { \"\$date\": \"${TODAY}\"} } }\' \
  --fields=text \
  --type=json)

ssh asmir@************ "${CMD}" | gzip > yesterday_${YESTERDAY_DATE}.json.gz

scp ubuntu@********:/home/<USER>/yesterday_2025-08-14.json.gz .