# --- VARIABLES --- #

# --- General Variables --- #

variable "environment" {
  type        = string
  description = "Name of the environment (e.g., shared, prod)."
  default     = "shared"
}

variable "product" {
  type        = string
  description = "Name of the project."
  default     = "coinpoker"
}

variable "region" {
  type        = string
  description = "AWS region to deploy resources in."
  default     = "eu-west-1"
}

variable "availability_zones" {
  type        = list(string)
  description = "List of availability zones to use."
  default     = ["eu-west-1a", "eu-west-1b", "eu-west-1c"]
}

# --- Default Tags --- #

variable "default_tags" {
  type        = map(string)
  description = "Default tags to apply to all resources"
  default = {
    Account       = "Coinpoker prod"
    Team          = "Platform"
    Owner         = "Blocklabs"
    ProvisionedBy = "terraform"
    Product       = "conpoker"
  }
}

# --- VPC Configuration --- #

variable "coinpoker_shared_vpc_configs" {
  type = map(object({
    cidr_block            = string # VPC CIDR range
    public_subnet_count   = number # Number of public subnets
    private_subnet_count  = number # Number of private subnets
    isolated_subnet_count = number # Number of isolated subnets
    environment           = string # Environment tag
    is_shared             = bool   # Whether VPC is shared
    transit_gateway_id    = string
    tags                  = map(string) # VPC-specific tags
  }))

  description = "VPC configuration including networking setup"

  default = {
    main = {
      cidr_block            = "********/16"
      public_subnet_count   = 2
      private_subnet_count  = 2
      isolated_subnet_count = 2
      environment           = ""
      is_shared             = true
      transit_gateway_id    = "tgw-079b51f9c2ba38973"
      tags = {
        Name        = "vpc"
        Environment = "shared"
      }
    }
  }
}

variable "coinpoker_shared_alb_configs" {
  type = map(object({
    load_balancer        = bool
    vpc                  = string
    name                 = string       # ALB name (e.g., "main-alb")
    environment          = string       # Environment name (e.g., "shared", "prod")
    is_shared            = bool         # If true, ALB is shared across environments
    project              = string       # Project name
    internal             = bool         # If true, ALB is internal; if false, it is internet-facing
    acm_certificate_arn  = string       # ARN of the ACM certificate for HTTPS listener
    enable_http_redirect = bool         # If true, creates an HTTP listener that redirects to HTTPS
    additional_certs     = list(string) # List of ARNs for additional certificates
    tags                 = map(string)
  }))

  description = "Configuration for ALB settings."

  default = {
    main = {
      load_balancer        = true
      vpc                  = "main"
      name                 = "main"
      environment          = ""
      is_shared            = true
      project              = "coinpoker"
      internal             = false
      acm_certificate_arn  = "arn:aws:acm:eu-west-1:116645453638:certificate/09f5823f-f622-4e57-a4b7-9f8b4c8845a4"
      enable_http_redirect = true
      additional_certs     = []
      tags = {
        Name        = "alb"
        Environment = "prod"
      }
    }
  }
}

# --- ECS Variables --- #

# variable "coinpoker_shared_ecs_configs" {
#   type = map(object({
#     vpc         = string
#     environment = string
#     is_shared   = bool
#     tags        = map(string)
#   }))

#   default = {
#     main = {
#       vpc         = "main"
#       environment = ""
#       is_shared   = true
#       tags = {
#         Name        = "ecs"
#         Environment = "shared"
#       }
#     }
#   }
# }

# --- ECS service Variables --- #

# variable "coinpoker_dev_ecs_service_configs" {
#   type = map(object({
#     vpc                     = string
#     ecs                     = optional(string)
#     cloudmap_enabled        = optional(bool)
#     service_connect_enabled = optional(bool)
#     ecr = object({
#       create_ecr = bool
#     })
#     service = object({
#       container_image       = string
#       project               = string
#       service_name          = string
#       environment           = string
#       is_shared             = bool
#       cpu                   = string
#       memory                = string
#       desired_count         = number
#       assign_public_ip      = bool
#       container_port        = number
#       environment_variables = list(map(string))
#       port_mappings = list(object({
#         containerPort = number
#         hostPort      = number
#         protocol      = string
#       }))
#     })
#     additional_containers = optional(list(object({
#       container_name        = string
#       name                  = optional(string)
#       image                 = string
#       essential             = optional(bool)
#       cpu                   = optional(number)
#       memory                = optional(number)
#       environment_variables = optional(list(map(string)))
#       port_mappings         = optional(list(any))
#       log_prefix            = optional(string)
#       log_retention_days    = optional(number)
#       depends_on = optional(list(object({
#         containerName = string
#         condition     = string
#       })))
#     })))
#     load_balancer = object({
#       enabled            = bool
#       load_balancer_name = string
#       domain_name        = string
#       listener_priority  = number
#       health_check_path  = string
#     })
#     tags = map(string)
#     iam = object({
#       iam_policies  = map(list(string))
#       iam_resources = map(list(string))
#     })
#   }))

#   description = "ECS service configuration map."

#   default = {
#     backend = {
#       vpc = "main"
#       ecr = {
#         create_ecr = false
#       }
#       service = {
#         container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-backend:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
#         project          = "coinpoker"
#         service_name     = "backend"
#         environment      = "prod"
#         is_shared        = false
#         cpu              = "1024"
#         memory           = "2048"
#         desired_count    = 1
#         assign_public_ip = false
#         container_port   = 3000
#         environment_variables = [
#           { name = "ENV", value = "prod" },
#           { name = "SERVICE", value = "coinpoker-backend" }
#         ]
#         port_mappings = [
#           {
#             containerPort = 3000
#             hostPort      = 3000
#             protocol      = "tcp"
#           }
#         ]
#       }
#       load_balancer = {
#         enabled            = true
#         load_balancer_name = "main"
#         domain_name        = "coinpoker-backend.dev.coinplatform.io"
#         listener_priority  = 10
#         health_check_path  = "/"
#       }
#       tags = {
#         Name        = "coinpoker-backend"
#         Environment = "prod"
#       }
#       iam = {
#         iam_policies = {
#           "ecr" = [
#             "ecr:GetAuthorizationToken",
#             "ecr:BatchCheckLayerAvailability",
#             "ecr:GetDownloadUrlForLayer",
#             "ecr:BatchGetImage"
#           ],
#           "logs" = [
#             "logs:CreateLogStream",
#             "logs:PutLogEvents",
#             "logs:CreateLogGroup"
#           ],
#           "ssm" = [
#             "ssmmessages:CreateControlChannel",
#             "ssmmessages:CreateDataChannel",
#             "ssmmessages:OpenControlChannel",
#             "ssmmessages:OpenDataChannel"
#           ]
#         }
#         iam_resources = {
#           "ecr" = [
#             "arn:aws:ecr:*:************:repository/*",
#             "*" # Required for GetAuthorizationToken which needs account-level permission
#           ],
#           "logs" = [
#             "arn:aws:logs:*:*:log-group:/aws/ecs/*",
#             "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
#           ]
#         }
#       }
#     }
#   }
# }

variable "cache_policy_configs" {
  description = "Configuration for CloudFront cache policies"
  type = map(object({
    name                  = string
    comment               = optional(string)
    default_ttl           = optional(number)
    max_ttl               = optional(number)
    min_ttl               = optional(number)
    enable_gzip           = optional(bool)
    enable_brotli         = optional(bool)
    cookie_behavior       = optional(string)
    cookies               = optional(list(string))
    header_behavior       = optional(string)
    headers               = optional(list(string))
    query_string_behavior = optional(string)
    query_strings         = optional(list(string))
  }))

  default = {
    one_day_policy = {
      name                  = "Static-CachingOptimized-1Day"
      comment               = "Optimized caching policy for static content with 1 day TTL"
      default_ttl           = 86400
      max_ttl               = 86400
      min_ttl               = 1
      enable_gzip           = true
      enable_brotli         = true
      cookie_behavior       = "none"
      header_behavior       = "none"
      query_string_behavior = "none"
    }
  }
}

variable "coinpoker_prod_cloudfront_configs" {
  type = map(object({
    environment = string
    is_shared   = bool
    origins = map(object({
      type                   = string # "alb", "s3", or "custom"
      alb_name               = optional(string)
      bucket_name            = optional(string)
      domain_name            = optional(string)
      origin_protocol_policy = optional(string)
      origin_ssl_protocols   = optional(list(string))
      custom_headers         = optional(map(string))
      name                   = string
    }))
    cloudfront_functions = optional(map(object({
      name    = string
      runtime = string
      code    = optional(string)
      comment = optional(string)
      publish = optional(bool)
    })))
    default_behavior = object({
      target_origin_key      = string
      allowed_methods        = list(string)
      cached_methods         = list(string)
      viewer_protocol_policy = string
      compress               = optional(bool)
      cache_policy_id        = string
      function_associations  = optional(map(string))
    })
    ordered_behaviors = map(object({
      path_pattern                 = string
      target_origin_key            = string
      allowed_methods              = list(string)
      cached_methods               = list(string)
      viewer_protocol_policy       = string
      compress                     = optional(bool)
      cache_policy_id              = string
      origin_request_policy_name   = optional(string)
      response_headers_policy_name = optional(string)
    }))
    custom_error_responses = map(object({
      error_code         = number
      response_code      = optional(number)
      response_page_path = optional(string)
    }))
    viewer_certificate = object({
      acm_certificate_arn      = string
      ssl_support_method       = string
      minimum_protocol_version = string
    })
    aliases  = list(string)
    waf_name = optional(string)
    tags     = map(string)
  }))

  default = {
    client-files-upload = {
      environment = "prod"
      is_shared   = false
      origins = {
        main = {
          type        = "s3"
          bucket_name = "coinpoker-prod-client-files-upload" # matches the s3 bucket name from coinpoker_dev_s3_configs
          domain_name = "coinpoker-client-files-upload.dev.coinplatform.io"
          name        = "client-files-upload"
        }
      }
      cloudfront_functions = {
        filename_rename = {
          name    = "filename-rename"
          runtime = "cloudfront-js-2.0"
          code    = "functions/filename-rename.js"
          comment = "Rename filename based on certain patterns"
        }
      }
      default_behavior = {
        target_origin_key      = "main"
        allowed_methods        = ["GET", "HEAD", "OPTIONS"]
        cached_methods         = ["GET", "HEAD"]
        viewer_protocol_policy = "redirect-to-https"
        compress               = true
        cache_policy_id        = "7d9ba53a-4107-488d-8e92-72d6543d09be" # Managed-CachingOptimized
        function_associations = {
          "viewer-response" = "filename_rename"
        }
      }
      ordered_behaviors = {}
      custom_error_responses = {
        "404" = {
          error_code         = 404
          response_code      = 200
          response_page_path = "/index.html"
        },
        "403" = {
          error_code         = 403
          response_code      = 200
          response_page_path = "/index.html"
        }
      }
      viewer_certificate = {
        acm_certificate_arn      = "arn:aws:acm:us-east-1:116645453638:certificate/e534e9a6-0840-4d94-8b35-2255671a9edb"
        ssl_support_method       = "sni-only"
        minimum_protocol_version = "TLSv1.2_2021"
      }
      aliases  = ["coinpoker-client-files-upload.coinplatform.io"]
      waf_name = "main"
      tags = {
        Name        = "coinpoker-prod-client-files-upload-cloudfront"
        Environment = "prod"
      }
    }
  }
}


variable "coinpoker_prod_s3_configs" {
  description = "Configuration for S3 buckets"
  type = map(object({
    name               = string
    environment        = string
    is_shared          = bool
    versioning_enabled = optional(bool)
    policy             = optional(string)
    cors_rules = optional(list(object({
      allowed_headers = optional(list(string))
      allowed_methods = list(string)
      allowed_origins = list(string)
      expose_headers  = optional(list(string))
      max_age_seconds = optional(number)
    })))
    lifecycle_rules = optional(list(object({
      id      = string
      enabled = optional(bool)
      transitions = optional(list(object({
        days          = optional(number)
        date          = optional(string)
        storage_class = string
      })))
      expiration = optional(object({
        days = optional(number)
        date = optional(string)
      }))
      noncurrent_version_transitions = optional(list(object({
        days          = number
        storage_class = string
      })))
      noncurrent_version_expiration = optional(object({
        days = number
      }))
    })))
    tags = map(string)
  }))

  default = {
    coinpoker-prod-client-files-upload = {
      name               = "client-files-upload"
      environment        = "prod"
      is_shared          = false
      versioning_enabled = true
      cors_rules = [
        {
          allowed_methods = ["GET", "HEAD"]
          allowed_origins = ["*"]
        }
      ]
      lifecycle_rules = []
      tags = {
        Name        = "client-files-upload"
        Environment = "prod"
      }
    }
  }
}

variable "coinpoker_efs_configs" {
  type = map(object({
    product             = string
    environment         = string
    vpc                 = string
    allowed_cidr_blocks = optional(list(string))
    access_points = map(object({
      path        = string
      owner_uid   = number
      owner_gid   = number
      permissions = string
      posix_user = object({
        gid            = number
        uid            = number
        secondary_gids = optional(list(number))
      })
    }))
    tags = map(string)
  }))

  description = "Configuration for EFS filesystems"

  default = {
    update = {
      product             = "coinpoker"
      environment         = "prod"
      vpc                 = "main"
      allowed_cidr_blocks = ["********/16"]
      access_points = {
        franchise = {
          path        = "/franchise"
          owner_uid   = 1000
          owner_gid   = 1000
          permissions = "755"
          posix_user = {
            gid = 1000
            uid = 1000
          }
        },
        pings = {
          path        = "/pings"
          owner_uid   = 1000
          owner_gid   = 1000
          permissions = "755"
          posix_user = {
            gid = 1000
            uid = 1000
          }
        },
        cache = {
          path        = "/cache"
          owner_uid   = 1000
          owner_gid   = 1000
          permissions = "755"
          posix_user = {
            gid = 1000
            uid = 1000
          }
        },
        assets = {
          path        = "/assets"
          owner_uid   = 1000
          owner_gid   = 1000
          permissions = "755"
          posix_user = {
            gid = 1000
            uid = 1000
          }
        }
      }
      tags = {
        Name        = "coinpoker-efs"
        Environment = "prod"
      }
    }
  }
}

# variable "pipelines" {
#   description = "Map of pipeline configurations"
#   type = map(object({
#     environment = string
#     project     = string
#     vpc         = optional(string, "")
#     subnets     = optional(list(string), [])
#     tags        = optional(map(string), {})
#     sources = map(object({
#       provider       = string
#       connection_arn = optional(string)
#       configuration  = map(any)
#     }))
#     stages = list(object({
#       name = string
#       type = string

#       build_config = optional(object({
#         alias                 = string
#         compute_type          = optional(string, "BUILD_GENERAL1_SMALL")
#         image                 = optional(string, "aws/codebuild/amazonlinux2-x86_64-standard:4.0")
#         environment_variables = optional(map(string), {})
#         buildspec             = optional(string, "buildspec.yml")
#       }))

#       deploy_config = optional(object({
#         compute_platform = optional(string, "ECS")
#         deployment_type  = optional(string, "ECS")
#         configuration    = map(any)
#       }))

#       approval_config = optional(object({
#         notification_arn = optional(string)
#         custom_message   = optional(string, "Please review and approve this deployment")
#       }))

#       aws_cli_config = optional(object({
#         commands              = list(string)
#         environment_variables = optional(map(string), {})
#         runtime_version       = optional(string, "aws/codebuild/amazonlinux2-x86_64-standard:4.0")
#       }))

#       depends_on_stages = optional(list(string), [])
#     }))
#   }))

#   default = {
#     init-pipeline = {
#       environment = "shared"
#       project     = "coinpoker"
#       vpc         = "main"
#       subnets     = []
#       tags = {
#         Environment = "prod"
#         Product     = "platform"
#         Purpose     = "Init Pipeline for Staging Environment for Platform team and Coinpoker staging account"
#       }
#       sources = {
#         main = {
#           provider       = "github"
#           connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/0920295e-63c1-4f2b-982a-f2f37f6f923c"
#           configuration = {
#             owner  = "BlockLabsDev"
#             repo   = "platform-terraform-templates"
#             branch = "main"
#           }
#         }
#       }
#       stages = [
#         {
#           name = "TerraformPlan"
#           type = "build"
#           build_config = {
#             alias        = "tf-plan"
#             compute_type = "BUILD_GENERAL1_SMALL"
#             image        = "aws/codebuild/standard:5.0"
#             environment_variables = {
#               ENVIRONMENT   = "prod"
#               TEMPLATE_PATH = "accounts/coinpoker/staging"

#             }
#             buildspec = "accounts/buildspec/infrastructure/buildspec_tf_plan.yml"
#           }
#         },
#         {
#           name = "Approval"
#           type = "approval"
#           approval_config = {
#             custom_message = "Please review and approve the Terraform plan"
#           }
#           depends_on_stages = ["TerraformPlan"]
#         },
#         {
#           name = "TerraformApply"
#           type = "build"
#           build_config = {
#             alias        = "tf-apply"
#             compute_type = "BUILD_GENERAL1_SMALL"
#             image        = "aws/codebuild/standard:5.0"
#             environment_variables = {
#               ENVIRONMENT   = "prod"
#               TEMPLATE_PATH = "accounts/coinpoker/staging"
#             }
#             buildspec = "accounts/buildspec/infrastructure/buildspec_tf_apply.yml"
#           }
#           depends_on_stages = ["Approval"]
#         }
#       ]
#     },
#     backend = {
#       environment = "prod"
#       project     = "coinpoker"
#       vpc         = "main"
#       tags = {
#         Environment = "prod"
#         Product     = "coinpoker"
#         Purpose     = "Pipeline for Coinpoker dev environment"
#       }
#       sources = {
#         main = {
#           provider       = "github"
#           connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
#           configuration = {
#             owner  = "BlockLabsDev"
#             repo   = "coinpoker-backend"
#             branch = "develop"
#           }
#         }
#       }
#       stages = [
#         {
#           name = "Build"
#           type = "build"
#           build_config = {
#             compute_type = "BUILD_GENERAL1_SMALL"
#             alias        = "build"
#             image        = "aws/codebuild/standard:5.0"
#             environment_variables = {
#               ENVIRONMENT    = "prod"
#               ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-backend"
#               AWS_ACCOUNT_ID = "************"
#               SERVICE_NAME   = "coinpoker-dev-backend"
#               SECRET_NAME    = "/dev/app/coinpoker/backend/env-vars"
#             }
#             buildspec = "accounts/buildspec/applications/buildspec_coinpoker_backend.yml"
#           }
#         },
#         {
#           name = "Deploy"
#           type = "deploy"
#           deploy_config = {
#             compute_platform = "ECS"
#             deployment_type  = "ECS"
#             configuration = {
#               ClusterName = "coinpoker-shared-ecs"
#               ServiceName = "coinpoker-dev-backend"
#               FileName    = "images.json"
#             }
#           }
#           depends_on_stages = ["Build"]
#         }
#       ]
#     },
#     frontend = {
#       name        = "frontend"
#       environment = "prod"
#       project     = "coinpoker"
#       vpc         = "main"
#       tags = {
#         Environment = "stg"
#         Product     = "coinfuture"
#         Purpose     = "Pipeline for Frontend static web in Coinpoker dev environment"
#       }
#       sources = {
#         main = {
#           provider       = "github"
#           connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
#           configuration = {
#             owner  = "BlockLabsDev"
#             repo   = "coinpoker-frontend"
#             branch = "develop"
#           }
#         }
#       }
#       stages = [
#         {
#           name = "Build"
#           type = "build"
#           build_config = {
#             compute_type = "BUILD_GENERAL1_SMALL"
#             alias        = "build"
#             image        = "aws/codebuild/standard:7.0"
#             environment_variables = {
#               ENVIRONMENT = "prod"
#               SECRET_NAME = "/dev/app/coinpoker/frontend/appconfig.json"
#             }
#             buildspec = "accounts/buildspec/applications/buildspec_coinpoker_frontend.yml"
#           }
#         },
#         {
#           name = "DeployStaticWeb"
#           type = "deploy"
#           deploy_config = {
#             deployment_type  = "S3" # Use S3 as provider
#             compute_platform = "S3" # Use S3 as compute platform
#             configuration = {
#               BucketName = "coinpoker-dev-frontend"
#               Extract    = "true" # If your artifacts are zipped
#             }
#           }
#           depends_on_stages = ["Build"]
#         },
#         # {
#         #   name = "InvalidateCache"
#         #   type = "aws_cli"
#         #   aws_cli_config = {
#         #     commands = [
#         #       "aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID --paths \"/*\""
#         #     ]
#         #     environment_variables = {
#         #       DISTRIBUTION_ID = "ETYIKPB0ERZ8J"
#         #     }
#         #   }
#         #   depends_on_stages = ["DeployStaticWeb"]
#         # }
#       ]
#     }
#   }
# }

variable "coinpoker_rabbitmq_configs" {
  type = map(object({
    vpc                 = string
    environment         = string
    is_shared           = bool
    instance_type       = string
    engine_version      = string
    admin_username      = string
    deployment_mode     = string
    publicly_accessible = bool
    allowed_cidr_blocks = list(string)
    tags                = map(string)
  }))

  default = {
    main = {
      vpc                 = "main"
      environment         = ""
      is_shared           = true
      instance_type       = "mq.t3.micro"
      engine_version      = "3.13"
      deployment_mode     = "ACTIVE_STANDBY_MULTI_AZ"
      admin_username      = "coinpoker"
      publicly_accessible = false
      allowed_cidr_blocks = ["********/16", "10.10.0.0/16"]
      tags = {
        Name        = "rabbitmq"
        Environment = "shared"
      }
    }
  }
}
