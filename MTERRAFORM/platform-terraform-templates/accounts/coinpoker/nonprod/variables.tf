# --- VARIABLES --- #

# --- General Variables --- #

variable "environment" {
  type        = string
  description = "Name of the environment (e.g., shared, prod)."
  default     = "shared"
}

variable "product" {
  type        = string
  description = "Name of the project."
  default     = "coinpoker"
}

variable "region" {
  type        = string
  description = "AWS region to deploy resources in."
  default     = "eu-west-1"
}

variable "availability_zones" {
  type        = list(string)
  description = "List of availability zones to use."
  default     = ["eu-west-1a", "eu-west-1b", "eu-west-1c"]
}

# --- Default Tags --- #

variable "default_tags" {
  type        = map(string)
  description = "Default tags to apply to all resources"
  default = {
    Account       = "Coinpoker staging"
    Team          = "Platform"
    Owner         = "Blocklabs"
    ProvisionedBy = "terraform"
    Product       = "conpoker"
  }
}

# --- VPC Configuration --- #

variable "coinpoker_shared_vpc_configs" {
  type = map(object({
    cidr_block            = string # VPC CIDR range
    public_subnet_count   = number # Number of public subnets
    private_subnet_count  = number # Number of private subnets
    isolated_subnet_count = number # Number of isolated subnets
    environment           = string # Environment tag
    is_shared             = bool   # Whether VPC is shared
    transit_gateway_id    = string
    tags                  = map(string) # VPC-specific tags
  }))

  description = "VPC configuration including networking setup"

  default = {
    main = {
      cidr_block            = "********/16"
      public_subnet_count   = 2
      private_subnet_count  = 2
      isolated_subnet_count = 2
      environment           = ""
      is_shared             = true
      transit_gateway_id    = "tgw-079b51f9c2ba38973"
      tags = {
        Name        = "vpc"
        Environment = "shared"
      }
    }
  }
}

variable "coinpoker_shared_alb_configs" {
  type = map(object({
    load_balancer        = bool
    vpc                  = string
    name                 = string       # ALB name (e.g., "main-alb")
    environment          = string       # Environment name (e.g., "shared", "prod")
    is_shared            = bool         # If true, ALB is shared across environments
    project              = string       # Project name
    internal             = bool         # If true, ALB is internal; if false, it is internet-facing
    acm_certificate_arn  = string       # ARN of the ACM certificate for HTTPS listener
    enable_http_redirect = bool         # If true, creates an HTTP listener that redirects to HTTPS
    additional_certs     = list(string) # List of ARNs for additional certificates
    tags                 = map(string)
  }))

  description = "Configuration for ALB settings."

  default = {
    main = {
      load_balancer        = true
      vpc                  = "main"
      name                 = "main"
      environment          = ""
      is_shared            = true
      project              = "coinpoker"
      internal             = false
      acm_certificate_arn  = "arn:aws:acm:eu-west-1:************:certificate/6a170b85-84ca-4b72-9070-ec2ee23f6a0b"
      enable_http_redirect = true
      additional_certs     = ["arn:aws:acm:eu-west-1:************:certificate/afc71052-6163-4908-b1fa-73616f77f767", "arn:aws:acm:eu-west-1:************:certificate/73219ec0-bc0f-4367-948e-1aabe7c17f3a"]
      tags = {
        Name        = "alb"
        Environment = "dev"
      }
    }
  }
}

# --- ECS Variables --- #

variable "coinpoker_shared_ecs_configs" {
  type = map(object({
    vpc         = string
    environment = string
    is_shared   = bool
    tags        = map(string)
  }))

  default = {
    main = {
      vpc         = "main"
      environment = ""
      is_shared   = true
      tags = {
        Name        = "ecs"
        Environment = "shared"
      }
    }
  }
}

# --- ECS service Variables --- #

variable "coinpoker_dev_ecs_service_configs" {
  type = map(object({
    vpc                     = string
    ecs                     = optional(string)
    cloudmap_enabled        = optional(bool)
    service_connect_enabled = optional(bool)
    ecr = object({
      create_ecr = bool
    })
    service = object({
      container_image       = string
      project               = string
      service_name          = string
      environment           = string
      is_shared             = bool
      cpu                   = string
      memory                = string
      desired_count         = number
      assign_public_ip      = bool
      container_port        = number
      environment_variables = list(map(string))
      command               = optional(list(string))
      port_mappings = list(object({
        containerPort = number
        hostPort      = number
        protocol      = string
      }))
      volumes = optional(list(object({
        name = string
        efs_volume_configuration = optional(object({
          file_system_id = string
          authorization_config = object({
            access_point_id = string
          })
        }))
      })))
      mount_points = optional(list(object({
        sourceVolume  = string
        containerPath = string
        readOnly      = bool
      })))
    })
    additional_containers = optional(list(object({
      container_name        = string
      name                  = optional(string)
      image                 = string
      essential             = optional(bool)
      cpu                   = optional(number)
      memory                = optional(number)
      command               = optional(list(string))
      environment_variables = optional(list(map(string)))
      depends_on_container = optional(list(object({
        containerName = string
        condition     = string
      })))
      healthCheck = optional(object({
        command     = list(string)
        interval    = number
        timeout     = number
        retries     = number
        startPeriod = optional(number)
      }))
      secrets = optional(list(object({
        name      = string
        valueFrom = string
      })))
      port_mappings = optional(list(object({
        containerPort = number
        hostPort      = number
        protocol      = string
      })))
      log_prefix         = optional(string)
      log_retention_days = optional(number)

      load_balancer = optional(object({
        enabled           = bool
        container_port    = number
        domain_name       = list(string)
        listener_priority = number
        health_check_path = string
        path_patterns     = optional(list(string))
      }))
    })))
    load_balancer = object({
      enabled            = bool
      load_balancer_name = string
      domain_name        = list(string)
      listener_priority  = number
      health_check_path  = string
    })
    tags = map(string)
    iam = object({
      iam_policies  = map(list(string))
      iam_resources = map(list(string))
    })
  }))

  description = "ECS service configuration map."

  default = {
    backend = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-backend:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "backend"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5014
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-backend" }
        ]
        port_mappings = [
          {
            containerPort = 5014
            hostPort      = 5014
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-backend.dev.coinplatform.io"]
        listener_priority  = 10
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-backend"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    update = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-update:latest"
        project          = "coinpoker"
        service_name     = "update"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 80
        environment_variables = [
          { name = "SERVICE", value = "update" }
        ]
        port_mappings = [
          {
            containerPort = 80
            hostPort      = 80
            protocol      = "tcp"
          },
          {
            containerPort = 9253
            hostPort      = 9253
            protocol      = "tcp"
          }
        ]
        mount_points = [
          {
            sourceVolume  = "franchise"
            containerPath = "/var/www/html/franchise"
            readOnly      = false
          },
          {
            sourceVolume  = "pings"
            containerPath = "/var/www/html/pings"
            readOnly      = false
          },
          {
            sourceVolume  = "cache"
            containerPath = "/var/www/html/cache"
            readOnly      = false
          },
          {
            sourceVolume  = "assets"
            containerPath = "/var/www/html/assets"
            readOnly      = false
          }
        ]
        volumes = [
          {
            name = "franchise"
            efs_volume_configuration = {
              file_system_id = "fs-00f640cf8ba345471" # Replace with your EFS ID
              authorization_config = {
                access_point_id = "fsap-08a897a82630a7cda" # Replace with your access point ID
              }
            }
          },
          {
            name = "pings"
            efs_volume_configuration = {
              file_system_id = "fs-00f640cf8ba345471" # Replace with your EFS ID
              authorization_config = {
                access_point_id = "fsap-07b296e73bd42322a" # Replace with your access point ID
              }
            }
          },
          {
            name = "cache"
            efs_volume_configuration = {
              file_system_id = "fs-00f640cf8ba345471" # Replace with your EFS ID
              authorization_config = {
                access_point_id = "fsap-0411637d5e87ac03d" # Replace with your access point ID
              }
            }
          },
          {
            name = "assets"
            efs_volume_configuration = {
              file_system_id = "fs-00f640cf8ba345471" # Replace with your EFS ID
              authorization_config = {
                access_point_id = "fsap-0ab1a9801f4cff396" # Replace with your access point ID
              }
            }
          }
        ]
      }
      additional_containers = [
        # {
        #   name = "exporter"

        #   container_name = "exporter"
        #   image          = "hipages/php-fpm_exporter:2.2.0"
        #   cpu            = 256
        #   memory         = 512
        #   environment_variables = [
        #     { name = "PHP_FPM_SCRAPE_URI", value = "http://localhost/status" },
        #   ]
        #   port_mappings = [
        #     {
        #       containerPort = 9253
        #       hostPort      = 9253
        #       protocol      = "tcp"
        #     }
        #   ]
        #   load_balancer = {
        #     enabled            = true
        #     load_balancer_name = "main"
        #     container_port     = 9253
        #     domain_name        = ["coinpoker-update-exporter.dev.coinplatform.io"]
        #     listener_priority  = 130
        #     health_check_path  = "/"
        #     path_patterns      = ["/*"]
        #   }
        # }
      ]
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["external-dev.coinpoker.com", "dev.update.coinpoker.com", "dev.update.coinpokerbackend.com", "update.dev.coinplatform.io"]
        listener_priority  = 110
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ],
          "ssm" = [
            "*"
          ]
        }
      }
    },
    auth-session = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-auth-session:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "auth-session"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5015
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-auth-session" }
        ]
        port_mappings = [
          {
            containerPort = 5015
            hostPort      = 5015
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-auth-session.dev.coinplatform.io"]
        listener_priority  = 11
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-auth-session"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    proxy-server = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-proxy-server:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "proxy-server"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-proxy-server" }
        ]
        command = ["./serverproxy", "1"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-proxy-server.dev.coinplatform.io"]
        listener_priority  = 12
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-proxy-server"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    accounting = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-accounting:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "accounting"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-accounting" }
        ]
        command = ["./serveraccounting", "28"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-accounting.dev.coinplatform.io"]
        listener_priority  = 13
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-accounting"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    authentication = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-authentication:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "authentication"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-authentication" }
        ]
        command = ["./serverauthentication", "3"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-authentication.dev.coinplatform.io"]
        listener_priority  = 13
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-authentication"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    poker-operation = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-operation:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "poker-operation"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-poker-operation" }
        ]
        command = ["./pokeroper", "32"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-poker-operation.dev.coinplatform.io"]
        listener_priority  = 13
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-poker-operation"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    server-lobby = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-server-lobby:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "server-lobby"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-server-lobby" }
        ]
        command = ["./lobby", "5"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-server-lobby.dev.coinplatform.io"]
        listener_priority  = 13
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-server-lobby"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    poker-lobby = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-lobby:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "poker-lobby"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-poker-lobby" }
        ]
        command = ["./spinlobby", "31"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-poker-lobby.dev.coinplatform.io"]
        listener_priority  = 13
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-poker-lobby"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    server-statistics = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-server-statistics:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "server-statistics"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 6100
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-server-statistics" },
          { name = "WEB_SERVICE_PORT", value = "6100" }
        ]
        command = ["./server_stats", "1783"]
        port_mappings = [
          {
            containerPort = 6100
            hostPort      = 6100
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-server-statistics.dev.coinplatform.io"]
        listener_priority  = 14
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-server-statistics"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    poker-history = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-history:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "poker-history"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-poker-history" }
        ]
        command = ["./handhistory", "37"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-poker-history.dev.coinplatform.io"]
        listener_priority  = 15
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-poker-history"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    server-chat = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-server-chat:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "server-chat"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-server-chat" }
        ]
        command = ["./chat", "22"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-server-chat.dev.coinplatform.io"]
        listener_priority  = 14
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-server-chat"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    customerio-pub = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-customerio-pub:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "customerio-pub"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5030
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-customerio-pub" }
        ]
        port_mappings = [
          {
            containerPort = 5030
            hostPort      = 5030
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-customerio-publisher.dev.coinplatform.io"]
        listener_priority  = 15
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-customerio-pub"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    poker-api = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-api:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "poker-api"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5001
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-poker-api" },
          { name = "NODE_ENV", value = "development" },
          { name = "RUN_MODE", value = "api" }
        ]
        port_mappings = [
          {
            containerPort = 5001
            hostPort      = 5001
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-poker-api.dev.coinplatform.io"]
        listener_priority  = 16
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-poker-api"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    poker-init = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-init:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "poker-init"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5000
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-poker-init" },
          { name = "NODE_ENV", value = "development" },
          { name = "RUN_MODE", value = "init" }
        ]
        port_mappings = [
          {
            containerPort = 5000
            hostPort      = 5000
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-poker-init.dev.coinplatform.io"]
        listener_priority  = 18
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-poker-init"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    backend-public = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-backend-public:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "backend-public"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5012
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-backend-public" }
        ]
        port_mappings = [
          {
            containerPort = 5012
            hostPort      = 5012
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-backend-public.dev.coinplatform.io"]
        listener_priority  = 17
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-backend-public"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    ofcp-backoffice = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-ofcp-backoffice:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "ofcp-backoffice"
        environment      = "dev"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5020
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-ofcp-backoffice" },
          { name = "APACHE_PORT", value = 5020 },
          { name = "BASIC_AUTH", value = 0 }
        ]
        port_mappings = [
          {
            containerPort = 5020
            hostPort      = 5020
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-php-ofcp-backoffice.dev.coinplatform.io"]
        listener_priority  = 21
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-php-ofcp-backoffice"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    backoffice-mt = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-backoffice-mt:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "backoffice-mt"
        environment      = "dev"
        is_shared        = false
        cpu              = "2048"
        memory           = "4096"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 80
        environment_variables = [
          { name = "ENV", value = "dev" },
          { name = "SERVICE", value = "coinpoker-backoffice-mt" }
        ]
        port_mappings = [
          {
            containerPort = 80
            hostPort      = 80
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-backoffice-multitenant.dev.coinplatform.io"]
        listener_priority  = 22
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-backoffice-mt"
        Environment = "dev"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },

    ######### STAGING
    update-stg = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-update:latest"
        project          = "coinpoker"
        service_name     = "update"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 80
        environment_variables = [
          { name = "SERVICE", value = "update" }
        ]
        port_mappings = [
          {
            containerPort = 80
            hostPort      = 80
            protocol      = "tcp"
          }
        ]
        mount_points = [
          {
            sourceVolume  = "franchise"
            containerPath = "/var/www/html/franchise"
            readOnly      = false
          },
          {
            sourceVolume  = "pings"
            containerPath = "/var/www/html/pings"
            readOnly      = false
          },
          {
            sourceVolume  = "cache"
            containerPath = "/var/www/html/cache"
            readOnly      = false
          },
          {
            sourceVolume  = "assets"
            containerPath = "/var/www/html/assets"
            readOnly      = false
          }
        ]
        volumes = [
          {
            name = "franchise"
            efs_volume_configuration = {
              file_system_id = "fs-0ede65b3af9e52f43" # Replace with your EFS ID
              authorization_config = {
                access_point_id = "fsap-0aa3c94b85df05289" # Replace with your access point ID
              }
            }
          },
          {
            name = "pings"
            efs_volume_configuration = {
              file_system_id = "fs-0ede65b3af9e52f43" # Replace with your EFS ID
              authorization_config = {
                access_point_id = "fsap-0e5993f225cb20dad" # Replace with your access point ID
              }
            }
          },
          {
            name = "cache"
            efs_volume_configuration = {
              file_system_id = "fs-0ede65b3af9e52f43" # Replace with your EFS ID
              authorization_config = {
                access_point_id = "fsap-04ee87fdea5a0df62" # Replace with your access point ID
              }
            }
          },
          {
            name = "assets"
            efs_volume_configuration = {
              file_system_id = "fs-0ede65b3af9e52f43" # Replace with your EFS ID
              authorization_config = {
                access_point_id = "fsap-00671878a1ebd9e57" # Replace with your access point ID
              }
            }
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-update.stg.coinplatform.io"]
        listener_priority  = 111
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ],
          "ssm" = [
            "*"
          ]
        }
      }
    },
    backend-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-backend:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "backend"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5014
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-backend" }
        ]
        port_mappings = [
          {
            containerPort = 5014
            hostPort      = 5014
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-backend.stg.coinplatform.io"]
        listener_priority  = 122
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-backend"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },

    auth-session-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-auth-session:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "auth-session"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5015
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-auth-session" }
        ]
        port_mappings = [
          {
            containerPort = 5015
            hostPort      = 5015
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-auth-session.stg.coinplatform.io"]
        listener_priority  = 109
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-auth-session"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    proxy-server-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-proxy-server:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "proxy-server"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-proxy-server" }
        ]
        command = ["./serverproxy", "1"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-proxy-server.stg.coinplatform.io"]
        listener_priority  = 112
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-proxy-server"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    accounting-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-accounting:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "accounting"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-accounting" }
        ]
        command = ["./serveraccounting", "28"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-accounting.stg.coinplatform.io"]
        listener_priority  = 113
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-accounting"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    authentication-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-authentication:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "authentication"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-authentication" }
        ]
        command = ["./serverauthentication", "3"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-authentication.stg.coinplatform.io"]
        listener_priority  = 113
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-authentication"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    poker-operation-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-operation:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "poker-operation"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-poker-operation" }
        ]
        command = ["./pokeroper", "32"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-poker-operation.stg.coinplatform.io"]
        listener_priority  = 113
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-poker-operation"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    server-lobby-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-server-lobby:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "server-lobby"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-server-lobby" }
        ]
        command = ["./lobby", "5"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-server-lobby.stg.coinplatform.io"]
        listener_priority  = 113
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-server-lobby"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    poker-lobby-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-lobby:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "poker-lobby"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-poker-lobby" }
        ]
        command = ["./spinlobby", "31"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-poker-lobby.stg.coinplatform.io"]
        listener_priority  = 113
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-poker-lobby"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    server-statistics-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-server-statistics:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "server-statistics"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 6100
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-server-statistics" },
          { name = "WEB_SERVICE_PORT", value = "6100" }
        ]
        command = ["./server_stats", "1783"]
        port_mappings = [
          {
            containerPort = 6100
            hostPort      = 6100
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-server-statistics.stg.coinplatform.io"]
        listener_priority  = 114
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-server-statistics"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    poker-history-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-history:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "poker-history"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-poker-history" }
        ]
        command = ["./handhistory", "37"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-poker-history.stg.coinplatform.io"]
        listener_priority  = 115
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-poker-history"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    server-chat-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-server-chat:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "server-chat"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 8080
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-server-chat" }
        ]
        command = ["./chat", "22"]
        port_mappings = [
          {
            containerPort = 8080
            hostPort      = 8080
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = false
        load_balancer_name = "main"
        domain_name        = ["coinpoker-server-chat.stg.coinplatform.io"]
        listener_priority  = 114
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-server-chat"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    customerio-pub-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-customerio-pub:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "customerio-pub"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5030
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-customerio-pub" }
        ]
        port_mappings = [
          {
            containerPort = 5030
            hostPort      = 5030
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-customerio-publisher.stg.coinplatform.io"]
        listener_priority  = 115
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-customerio-pub"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    poker-api-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-api:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "poker-api"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5001
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-poker-api" },
          { name = "NODE_ENV", value = "staging" },
          { name = "RUN_MODE", value = "api" }
        ]
        port_mappings = [
          {
            containerPort = 5001
            hostPort      = 5001
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-poker-api.stg.coinplatform.io"]
        listener_priority  = 116
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-poker-api"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    poker-init-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-init:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "poker-init"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5000
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-poker-init" },
          { name = "NODE_ENV", value = "staging" },
          { name = "RUN_MODE", value = "init" }
        ]
        port_mappings = [
          {
            containerPort = 5000
            hostPort      = 5000
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-poker-init.stg.coinplatform.io"]
        listener_priority  = 118
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-poker-init"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    backend-public-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-backend-public:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "backend-public"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5012
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-backend-public" }
        ]
        port_mappings = [
          {
            containerPort = 5012
            hostPort      = 5012
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-backend-public.stg.coinplatform.io"]
        listener_priority  = 117
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-backend-public"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    },
    ofcp-backoffice-s = {
      vpc = "main"
      ecr = {
        create_ecr = false
      }
      service = {
        container_image  = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-ofcp-backoffice:latest" # "${module.ecr[each.key].repo_urls[each.key]}:latest"
        project          = "coinpoker"
        service_name     = "ofcp-backoffice"
        environment      = "stg"
        is_shared        = false
        cpu              = "1024"
        memory           = "2048"
        desired_count    = 1
        assign_public_ip = false
        container_port   = 5020
        environment_variables = [
          { name = "ENV", value = "stg" },
          { name = "SERVICE", value = "coinpoker-ofcp-backoffice" },
          { name = "APACHE_PORT", value = 5020 },
          { name = "BASIC_AUTH", value = 0 }
        ]
        port_mappings = [
          {
            containerPort = 5020
            hostPort      = 5020
            protocol      = "tcp"
          }
        ]
      }
      load_balancer = {
        enabled            = true
        load_balancer_name = "main"
        domain_name        = ["coinpoker-php-ofcp-backoffice.stg.coinplatform.io"]
        listener_priority  = 121
        health_check_path  = "/"
      }
      tags = {
        Name        = "coinpoker-php-ofcp-backoffice"
        Environment = "stg"
      }
      iam = {
        iam_policies = {
          "ecr" = [
            "ecr:GetAuthorizationToken",
            "ecr:BatchCheckLayerAvailability",
            "ecr:GetDownloadUrlForLayer",
            "ecr:BatchGetImage"
          ],
          "logs" = [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:CreateLogGroup"
          ],
          "ssm" = [
            "ssmmessages:CreateControlChannel",
            "ssmmessages:CreateDataChannel",
            "ssmmessages:OpenControlChannel",
            "ssmmessages:OpenDataChannel"
          ]
        }
        iam_resources = {
          "ecr" = [
            "arn:aws:ecr:*:************:repository/*",
            "*" # Required for GetAuthorizationToken which needs account-level permission
          ],
          "logs" = [
            "arn:aws:logs:*:*:log-group:/aws/ecs/*",
            "arn:aws:logs:*:*:log-group:/aws/ecs/*:log-stream:*"
          ]
        }
      }
    }
  }
}

variable "coinpoker_efs_configs" {
  type = map(object({
    product             = string
    environment         = string
    vpc                 = string
    allowed_cidr_blocks = optional(list(string))
    access_points = map(object({
      path        = string
      owner_uid   = number
      owner_gid   = number
      permissions = string
      posix_user = object({
        gid            = number
        uid            = number
        secondary_gids = optional(list(number))
      })
    }))
    tags = map(string)
  }))

  description = "Configuration for EFS filesystems"

  default = {
    update = {
      product             = "coinpoker"
      environment         = "dev"
      vpc                 = "main"
      allowed_cidr_blocks = ["********/16"]
      access_points = {
        franchise = {
          path        = "/franchise"
          owner_uid   = 1000
          owner_gid   = 1000
          permissions = "755"
          posix_user = {
            gid = 1000
            uid = 1000
          }
        },
        pings = {
          path        = "/pings"
          owner_uid   = 1000
          owner_gid   = 1000
          permissions = "755"
          posix_user = {
            gid = 1000
            uid = 1000
          }
        },
        cache = {
          path        = "/cache"
          owner_uid   = 1000
          owner_gid   = 1000
          permissions = "755"
          posix_user = {
            gid = 1000
            uid = 1000
          }
        },
        assets = {
          path        = "/assets"
          owner_uid   = 1000
          owner_gid   = 1000
          permissions = "755"
          posix_user = {
            gid = 1000
            uid = 1000
          }
        }
      }
      tags = {
        Name        = "coinpoker-efs"
        Environment = "dev"
      }
    }
    update-stg = {
      product             = "coinpoker"
      environment         = "stg"
      vpc                 = "main"
      allowed_cidr_blocks = ["********/16"]
      access_points = {
        franchise = {
          path        = "/franchise"
          owner_uid   = 1000
          owner_gid   = 1000
          permissions = "755"
          posix_user = {
            gid = 1000
            uid = 1000
          }
        },
        pings = {
          path        = "/pings"
          owner_uid   = 1000
          owner_gid   = 1000
          permissions = "755"
          posix_user = {
            gid = 1000
            uid = 1000
          }
        },
        cache = {
          path        = "/cache"
          owner_uid   = 1000
          owner_gid   = 1000
          permissions = "755"
          posix_user = {
            gid = 1000
            uid = 1000
          }
        },
        assets = {
          path        = "/assets"
          owner_uid   = 1000
          owner_gid   = 1000
          permissions = "755"
          posix_user = {
            gid = 1000
            uid = 1000
          }
        }
      }
      tags = {
        Name        = "coinpoker-efs"
        Environment = "stg"
      }
    }
  }
}

variable "cache_policy_configs" {
  description = "Configuration for CloudFront cache policies"
  type = map(object({
    name                  = string
    comment               = optional(string)
    default_ttl           = optional(number)
    max_ttl               = optional(number)
    min_ttl               = optional(number)
    enable_gzip           = optional(bool)
    enable_brotli         = optional(bool)
    cookie_behavior       = optional(string)
    cookies               = optional(list(string))
    header_behavior       = optional(string)
    headers               = optional(list(string))
    query_string_behavior = optional(string)
    query_strings         = optional(list(string))
  }))

  default = {
    one_day_policy = {
      name                  = "Static-CachingOptimized-1Day"
      comment               = "Optimized caching policy for static content with 1 day TTL"
      default_ttl           = 86400
      max_ttl               = 86400
      min_ttl               = 86400
      enable_gzip           = true
      enable_brotli         = true
      cookie_behavior       = "none"
      header_behavior       = "whitelist"
      headers               = ["Host"]
      query_string_behavior = "all"
    }
  }
}

variable "response_headers_policy_configs" {
  description = "Configuration for CloudFront response headers policies"
  type = map(object({
    name    = string
    comment = optional(string)
    cors_config = optional(object({
      access_control_allow_credentials = bool
      access_control_allow_headers     = list(string)
      access_control_allow_methods     = list(string)
      access_control_allow_origins     = list(string)
      access_control_expose_headers    = optional(list(string))
      access_control_max_age_sec       = number
      origin_override                  = bool
    }))
    custom_headers = optional(list(object({
      header   = string
      value    = string
      override = bool
    })))
    remove_headers = optional(list(object({
      header = string
    })))
    security_headers_config = optional(object({
      content_type_options = optional(object({
        override = bool
      }))
      frame_options = optional(object({
        frame_option = string
        override     = bool
      }))
      referrer_policy = optional(object({
        referrer_policy = string
        override        = bool
      }))
      strict_transport_security = optional(object({
        access_control_max_age_sec = number
        include_subdomains         = optional(bool)
        preload                    = optional(bool)
        override                   = bool
      }))
      content_security_policy = optional(object({
        content_security_policy = string
        override                = bool
      }))
    }))
    server_timing_headers_config = optional(object({
      enabled       = bool
      sampling_rate = number
    }))
  }))

  default = {
    cors_policy = {
      name    = "cors"
      comment = "CORS policy for test environment"
      cors_config = {
        access_control_allow_credentials = false
        access_control_allow_headers     = ["*"]
        access_control_allow_methods     = ["ALL"]
        access_control_allow_origins     = ["*"]
        access_control_expose_headers    = []
        access_control_max_age_sec       = 600
        origin_override                  = true
      }
    }
    security_policy = {
      name    = "security-headers"
      comment = "Security headers policy"
      security_headers_config = {
        content_type_options = {
          override = false
        }
        frame_options = {
          frame_option = "DENY"
          override     = false
        }
        referrer_policy = {
          referrer_policy = "strict-origin-when-cross-origin"
          override        = false
        }
        strict_transport_security = {
          access_control_max_age_sec = 31536000
          include_subdomains         = true
          preload                    = false
          override                   = false
        }
      }
    }
  }
}

variable "coinpoker_dev_cloudfront_configs" {
  type = map(object({
    environment = string
    is_shared   = bool
    origins = map(object({
      type                     = string # "alb", "s3", or "custom"
      alb_name                 = optional(string)
      bucket_name              = optional(string)
      domain_name              = optional(string)
      origin_protocol_policy   = optional(string)
      origin_ssl_protocols     = optional(list(string))
      custom_headers           = optional(map(string))
      name                     = string
      http_port                = optional(number, 80)
      https_port               = optional(number, 443)
      origin_keepalive_timeout = optional(number, 60)
      origin_read_timeout      = optional(number, 30)
    }))
    default_behavior = object({
      target_origin_key          = string
      allowed_methods            = list(string)
      cached_methods             = list(string)
      viewer_protocol_policy     = string
      compress                   = optional(bool)
      cache_policy_id            = string
      function_associations      = optional(map(string))
      response_headers_policy_id = optional(string)
    })
    ordered_behaviors = map(object({
      path_pattern                 = string
      target_origin_key            = string
      allowed_methods              = list(string)
      cached_methods               = list(string)
      viewer_protocol_policy       = string
      compress                     = optional(bool)
      cache_policy_id              = string
      origin_request_policy_name   = optional(string)
      response_headers_policy_name = optional(string)
    }))
    custom_error_responses = map(object({
      error_code         = number
      response_code      = optional(number)
      response_page_path = optional(string)
    }))
    viewer_certificate = object({
      acm_certificate_arn      = string
      ssl_support_method       = string
      minimum_protocol_version = string
    })
    aliases  = list(string)
    waf_name = optional(string)
    tags     = map(string)
  }))

  default = {
    frontend = {
      environment = "dev"
      is_shared   = false
      origins = {
        main = {
          type        = "s3"
          bucket_name = "coinpoker-dev-frontend" # matches the s3 bucket name from coinpoker_dev_s3_configs
          domain_name = "coinpoker-frontend.dev.coinplatform.io"
          name        = "frontend"
        }
      }
      default_behavior = {
        target_origin_key      = "main"
        allowed_methods        = ["GET", "HEAD", "OPTIONS"]
        cached_methods         = ["GET", "HEAD"]
        viewer_protocol_policy = "redirect-to-https"
        compress               = true
        cache_policy_id        = "4135ea2d-6df8-44a3-9df3-4b5a84be39ad" # Static-CachingOptimized-1Day
      }
      ordered_behaviors = {}
      custom_error_responses = {
        "404" = {
          error_code         = 404
          response_code      = 200
          response_page_path = "/index.html"
        },
        "403" = {
          error_code         = 403
          response_code      = 200
          response_page_path = "/index.html"
        }
      }
      viewer_certificate = {
        acm_certificate_arn      = "arn:aws:acm:us-east-1:************:certificate/b155c56f-2f52-4a2a-9a2a-ed710a1b3315"
        ssl_support_method       = "sni-only"
        minimum_protocol_version = "TLSv1.2_2021"
      }
      aliases  = ["coinpoker-frontend.dev.coinplatform.io"]
      waf_name = "main"
      tags = {
        Name        = "coinpoker-dev-frontend-cloudfront"
        Environment = "dev"
      }
    },
    update = {
      environment = "dev"
      is_shared   = false
      origins = {
        main = {
          type                     = "alb"
          alb_name                 = "main"
          domain_name              = "coinpoker-update.dev.coinplatform.io"
          origin_protocol_policy   = "https-only"
          origin_ssl_protocols     = ["TLSv1.2"]
          name                     = "update"
          http_port                = 80
          https_port               = 443
          origin_keepalive_timeout = 60
          origin_read_timeout      = 30
        }
      }
      default_behavior = {
        target_origin_key          = "main"
        allowed_methods            = ["GET", "HEAD", "OPTIONS", "DELETE", "PATCH", "PUT", "POST"]
        cached_methods             = ["GET", "HEAD"]
        viewer_protocol_policy     = "redirect-to-https"
        compress                   = true
        cache_policy_id            = "bd298306-3a05-4090-81e1-54db2540f6c8" # Managed-CachingOptimized
        response_headers_policy_id = "7552a0bd-0ff9-4a73-9602-06c6fca5aeef" # security-headers
      }
      ordered_behaviors = {
        "php-fpm" = {
          path_pattern           = "/status/php-fpm"
          target_origin_key      = "main"
          allowed_methods        = ["GET", "HEAD", "OPTIONS"]
          cached_methods         = ["GET", "HEAD"]
          viewer_protocol_policy = "redirect-to-https"
          compress               = true
          cache_policy_id        = "4135ea2d-6df8-44a3-9df3-4b5a84be39ad" # CachingDisabled
        },
        "ping" = {
          path_pattern           = "/ping"
          target_origin_key      = "main"
          allowed_methods        = ["GET", "HEAD", "OPTIONS"]
          cached_methods         = ["GET", "HEAD"]
          viewer_protocol_policy = "redirect-to-https"
          compress               = true
          cache_policy_id        = "4135ea2d-6df8-44a3-9df3-4b5a84be39ad" # CachingDisabled
        }
      }
      custom_error_responses = {
        "404" = {
          error_code         = 404
          response_code      = 200
          response_page_path = "/index.html"
        },
        "403" = {
          error_code         = 403
          response_code      = 200
          response_page_path = "/index.html"
        }
      }
      viewer_certificate = {
        acm_certificate_arn      = "arn:aws:acm:us-east-1:************:certificate/3e31ecfa-1fa4-416a-8c46-813eda70e0e3"
        ssl_support_method       = "sni-only"
        minimum_protocol_version = "TLSv1.2_2021"
      }
      aliases  = ["external-dev.coinpoker.com", "dev.update.coinpoker.com", "dev.update.coinpokerbackend.com"]
      waf_name = "main"
      tags = {
        Name        = "coinpoker-dev-update-cloudfront"
        Environment = "dev"
      }
    },
    frontend-stg = {
      environment = "stg"
      is_shared   = false
      origins = {
        main = {
          type        = "s3"
          bucket_name = "coinpoker-stg-frontend" # matches the s3 bucket name from coinpoker_stg_s3_configs
          domain_name = "coinpoker-frontend.stg.coinplatform.io"
          name        = "frontend"
        }
      }
      default_behavior = {
        target_origin_key      = "main"
        allowed_methods        = ["GET", "HEAD", "OPTIONS"]
        cached_methods         = ["GET", "HEAD"]
        viewer_protocol_policy = "redirect-to-https"
        compress               = true
        cache_policy_id        = "4135ea2d-6df8-44a3-9df3-4b5a84be39ad" # Static-CachingOptimized-1Day
      }
      ordered_behaviors = {}
      custom_error_responses = {
        "404" = {
          error_code         = 404
          response_code      = 200
          response_page_path = "/index.html"
        },
        "403" = {
          error_code         = 403
          response_code      = 200
          response_page_path = "/index.html"
        }
      }
      viewer_certificate = {
        acm_certificate_arn      = "arn:aws:acm:us-east-1:************:certificate/2d1f7406-9915-4659-ba8f-ccbb7b999aac"
        ssl_support_method       = "sni-only"
        minimum_protocol_version = "TLSv1.2_2021"
      }
      aliases  = ["coinpoker-frontend.stg.coinplatform.io"]
      waf_name = "main"
      tags = {
        Name        = "coinpoker-stg-frontend-cloudfront"
        Environment = "stg"
      }
    },
    update-stg = {
      environment = "stg"
      is_shared   = false
      origins = {
        main = {
          type                     = "alb"
          alb_name                 = "main"
          domain_name              = "coinpoker-update.stg.coinplatform.io"
          origin_protocol_policy   = "https-only"
          origin_ssl_protocols     = ["TLSv1.2"]
          name                     = "update"
          http_port                = 80
          https_port               = 443
          origin_keepalive_timeout = 60
          origin_read_timeout      = 30
        }
      }
      default_behavior = {
        target_origin_key          = "main"
        allowed_methods            = ["GET", "HEAD", "OPTIONS", "DELETE", "PATCH", "PUT", "POST"]
        cached_methods             = ["GET", "HEAD"]
        viewer_protocol_policy     = "redirect-to-https"
        compress                   = true
        cache_policy_id            = "bd298306-3a05-4090-81e1-54db2540f6c8" # Managed-CachingOptimized
        response_headers_policy_id = "7552a0bd-0ff9-4a73-9602-06c6fca5aeef" # security-headers
      }
      ordered_behaviors = {}
      custom_error_responses = {
        "404" = {
          error_code         = 404
          response_code      = 200
          response_page_path = "/index.html"
        },
        "403" = {
          error_code         = 403
          response_code      = 200
          response_page_path = "/index.html"
        }
      }
      viewer_certificate = {
        acm_certificate_arn      = "arn:aws:acm:us-east-1:************:certificate/2d1f7406-9915-4659-ba8f-ccbb7b999aac"
        ssl_support_method       = "sni-only"
        minimum_protocol_version = "TLSv1.2_2021"
      }
      aliases  = ["coinpoker-update.stg.coinplatform.io"]
      waf_name = "main"
      tags = {
        Name        = "coinpoker-stg-update-cloudfront"
        Environment = "stg"
      }
    }
  }
}


variable "coinpoker_dev_s3_configs" {
  description = "Configuration for S3 buckets"
  type = map(object({
    name               = string
    environment        = string
    is_shared          = bool
    versioning_enabled = optional(bool)
    policy             = optional(string)
    cors_rules = optional(list(object({
      allowed_headers = optional(list(string))
      allowed_methods = list(string)
      allowed_origins = list(string)
      expose_headers  = optional(list(string))
      max_age_seconds = optional(number)
    })))
    lifecycle_rules = optional(list(object({
      id      = string
      enabled = optional(bool)
      transitions = optional(list(object({
        days          = optional(number)
        date          = optional(string)
        storage_class = string
      })))
      expiration = optional(object({
        days = optional(number)
        date = optional(string)
      }))
      noncurrent_version_transitions = optional(list(object({
        days          = number
        storage_class = string
      })))
      noncurrent_version_expiration = optional(object({
        days = number
      }))
    })))
    tags = map(string)
  }))

  default = {
    coinpoker-dev-frontend = {
      name               = "frontend"
      environment        = "dev"
      is_shared          = false
      versioning_enabled = true
      cors_rules = [
        {
          allowed_methods = ["GET", "HEAD"]
          allowed_origins = ["*"]
        }
      ]
      lifecycle_rules = []
      tags = {
        Name        = "frontend"
        Environment = "dev"
      }
    },
    coinpoker-stg-frontend = {
      name               = "frontend"
      environment        = "stg"
      is_shared          = false
      versioning_enabled = true
      cors_rules = [
        {
          allowed_methods = ["GET", "HEAD"]
          allowed_origins = ["*"]
        }
      ]
      lifecycle_rules = []
      tags = {
        Name        = "frontend"
        Environment = "stg"
      }
    }
  }
}


variable "pipelines" {
  description = "Map of pipeline configurations"
  type = map(object({
    environment = string
    project     = string
    vpc         = optional(string, "")
    subnets     = optional(list(string), [])
    tags        = optional(map(string), {})
    sources = map(object({
      provider       = string
      connection_arn = optional(string)
      configuration  = map(any)
    }))
    stages = list(object({
      name = string
      type = string

      build_config = optional(object({
        alias                 = string
        compute_type          = optional(string, "BUILD_GENERAL1_SMALL")
        image                 = optional(string, "aws/codebuild/amazonlinux2-x86_64-standard:4.0")
        environment_variables = optional(map(string), {})
        buildspec             = optional(string, "buildspec.yml")
      }))

      deploy_config = optional(object({
        compute_platform = optional(string, "ECS")
        deployment_type  = optional(string, "ECS")
        configuration    = map(any)
      }))

      approval_config = optional(object({
        notification_arn = optional(string)
        custom_message   = optional(string, "Please review and approve this deployment")
      }))

      aws_cli_config = optional(object({
        commands              = list(string)
        environment_variables = optional(map(string), {})
        runtime_version       = optional(string, "aws/codebuild/amazonlinux2-x86_64-standard:4.0")
      }))

      depends_on_stages = optional(list(string), [])
    }))
  }))

  default = {
    init-pipeline = {
      environment = "shared"
      project     = "coinpoker"
      vpc         = "main"
      subnets     = []
      tags = {
        Environment = "dev"
        Product     = "platform"
        Purpose     = "Init Pipeline for Staging Environment for Platform team and Coinpoker staging account"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/0920295e-63c1-4f2b-982a-f2f37f6f923c"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "platform-terraform-templates"
            branch = "main"
          }
        }
      }
      stages = [
        {
          name = "TerraformPlan"
          type = "build"
          build_config = {
            alias        = "tf-plan"
            compute_type = "BUILD_GENERAL1_SMALL"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT   = "prod"
              TEMPLATE_PATH = "accounts/coinpoker/staging"

            }
            buildspec = "accounts/buildspec/infrastructure/buildspec_tf_plan.yml"
          }
        },
        {
          name = "Approval"
          type = "approval"
          approval_config = {
            custom_message = "Please review and approve the Terraform plan"
          }
          depends_on_stages = ["TerraformPlan"]
        },
        {
          name = "TerraformApply"
          type = "build"
          build_config = {
            alias        = "tf-apply"
            compute_type = "BUILD_GENERAL1_SMALL"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT   = "dev"
              TEMPLATE_PATH = "accounts/coinpoker/staging"
            }
            buildspec = "accounts/buildspec/infrastructure/buildspec_tf_apply.yml"
          }
          depends_on_stages = ["Approval"]
        }
      ]
    },
    backend = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-backend"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT                = "dev"
              ECR_REPO                   = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-backend"
              AWS_ACCOUNT_ID             = "************"
              SERVICE_NAME               = "coinpoker-dev-backend-container"
              SECRET_NAME                = "/dev/app/coinpoker/backend/env-vars"
              PLAYER_SESSION_SECRET_NAME = "/dev/app/coinpoker/backend/player_session_public.pem"
              CONFIG_SECRET_NAME         = "/dev/app/coinpoker/backend/config.json"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_backend.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-backend"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    frontend = {
      name        = "frontend"
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinfuture"
        Purpose     = "Pipeline for Frontend static web in Coinpoker dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-frontend"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:7.0"
            environment_variables = {
              ENVIRONMENT        = "dev"
              SECRET_NAME        = "/dev/app/coinpoker/frontend/env"
              CONFIG_SECRET_NAME = "/dev/app/coinpoker/frontend/appconfig.json"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_frontend.yml"
          }
        },
        {
          name = "DeployStaticWeb"
          type = "deploy"
          deploy_config = {
            deployment_type  = "S3" # Use S3 as provider
            compute_platform = "S3" # Use S3 as compute platform
            configuration = {
              BucketName = "coinpoker-dev-frontend"
              Extract    = "true" # If your artifacts are zipped
            }
          }
          depends_on_stages = ["Build"]
        },
        {
          name = "InvalidateCache"
          type = "aws_cli"
          aws_cli_config = {
            commands = [
              "aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID --paths \"/*\""
            ]
            environment_variables = {
              DISTRIBUTION_ID = "ERMNOOYYE7BN"
            }
          }
          depends_on_stages = ["DeployStaticWeb"]
        }
      ]
    },
    update = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-web-client"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "dev"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-update"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-dev-update-container"
              SECRET_NAME    = "/dev/app/coinpoker/update/env-vars"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_update.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-update"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    auth-session = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-auth-session-signer"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "dev"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-auth-session"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-dev-auth-session-container"
              SECRET_NAME    = "/dev/app/coinpoker/auth-session/env-vars"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_auth_session.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-auth-session"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    third-party-libs = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker third-party-libs dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-third-party-libs"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "dev"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-third-party-libs"
              AWS_ACCOUNT_ID = "************"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_third_party_libs.yml"
          }
        },
        {
          name = "TriggerDependenciesPipeline"
          type = "aws_cli"
          aws_cli_config = {
            commands = [
              "aws codepipeline start-pipeline-execution --name $TRIGGER_PIPELINE"
            ]
            environment_variables = {
              TRIGGER_PIPELINE = "coinpoker-dev-dependencies-pipeline"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    dependencies = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker dependencies dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-dependencies"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT           = "dev"
              ECR_REPO              = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-dependencies"
              AWS_ACCOUNT_ID        = "************"
              DOWNLOAD_DBDUMP       = "/dev/app/coinpoker/dependencies/download_dbdump.sh"
              THIRD_PARTY_LIBS_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-third-party-libs"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_dependencies.yml"
          }
        },
        {
          name = "TriggerProviderServicesPipeline"
          type = "aws_cli"
          aws_cli_config = {
            commands = [
              "aws codepipeline start-pipeline-execution --name $TRIGGER_PIPELINE"
            ]
            environment_variables = {
              TRIGGER_PIPELINE = "coinpoker-dev-provider-services-pipeline"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    provider-services = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker provider-services dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-provider-services"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "dev"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-provider-services"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-dependencies"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_provider_services.yml"
          }
        }
      ]
    },
    proxy-server = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker proxy-server dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-proxy"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "dev"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-proxy-server"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-dependencies"
              SERVICE_NAME      = "coinpoker-dev-proxy-server-container"
              SECRET_NAME       = "/dev/app/coinpoker/proxy-server/modules.conf"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_proxy_server.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-proxy-server"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    accounting = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker accounting dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-accounting"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT            = "dev"
              ECR_REPO               = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-accounting"
              AWS_ACCOUNT_ID         = "************"
              DEPENDENCIES_REPO      = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-dependencies"
              PROVIDER_SERVICES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-provider-services"
              SERVICE_NAME           = "coinpoker-dev-accounting-container"
              SECRET_NAME            = "/dev/app/coinpoker/accounting/modules.conf"
              DOWNLOAD_DBDUMP        = "/dev/app/coinpoker/dependencies/download_dbdump.sh"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_accounting.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-accounting"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    authentication = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker authentication dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-authentication"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT            = "dev"
              ECR_REPO               = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-authentication"
              AWS_ACCOUNT_ID         = "************"
              DEPENDENCIES_REPO      = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-dependencies"
              PROVIDER_SERVICES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-provider-services"
              SERVICE_NAME           = "coinpoker-dev-authentication-container"
              SECRET_NAME            = "/dev/app/coinpoker/authentication/modules.conf"
              DOWNLOAD_DBDUMP        = "/dev/app/coinpoker/dependencies/download_dbdump.sh"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_authentication.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-authentication"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    poker-operation = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker poker-operation dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-poker-operation"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "dev"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-operation"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-dependencies"
              SERVICE_NAME      = "coinpoker-dev-poker-operation-container"
              SECRET_NAME       = "/dev/app/coinpoker/poker-operation/modules.conf"
              VERSION_TYPE      = "dev"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_poker_operation.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-poker-operation"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    poker-lobby = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker poker-lobby dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-poker-lobby"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "dev"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-lobby"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-dependencies"
              SERVICE_NAME      = "coinpoker-dev-poker-lobby-container"
              SECRET_NAME       = "/dev/app/coinpoker/poker-lobby/modules.conf"
              VERSION_TYPE      = "dev"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_poker_lobby.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-poker-lobby"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    server-lobby = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker server-lobby dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-lobby"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "dev"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-server-lobby"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-dependencies"
              SERVICE_NAME      = "coinpoker-dev-server-lobby-container"
              SECRET_NAME       = "/dev/app/coinpoker/server-lobby/modules.conf"
              VERSION_TYPE      = "dev"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_server_lobby.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-server-lobby"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    server-statistics = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker server-statistics dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-statistics"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "dev"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-server-statistics"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-dependencies"
              SERVICE_NAME      = "coinpoker-dev-server-statistics-container"
              SECRET_NAME       = "/dev/app/coinpoker/server-statistics/modules.conf"
              VERSION_TYPE      = "dev"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_server_statistics.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-server-statistics"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    poker-history = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker poker-history dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-poker-history"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "dev"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-history"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-dependencies"
              SERVICE_NAME      = "coinpoker-dev-poker-history-container"
              SECRET_NAME       = "/dev/app/coinpoker/server-poker-history/modules.conf"
              VERSION_TYPE      = "dev"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_server_poker_history.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-poker-history"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    server-chat = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker server-chat dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-chat"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "dev"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-server-chat"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-dependencies"
              SERVICE_NAME      = "coinpoker-dev-server-chat-container"
              SECRET_NAME       = "/dev/app/coinpoker/server-chat/modules.conf"
              VERSION_TYPE      = "dev"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_server_chat.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-server-chat"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    customerio-pub = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker customerio-pub dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-customerio-publisher"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "dev"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-customerio-pub"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-dev-customerio-pub-container"
              SECRET_NAME    = "/dev/app/coinpoker/customerio-publisher/cio.conf"
              VERSION_TYPE   = "dev"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_customerio_publisher.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-customerio-pub"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    poker-api = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker poker-api dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-poker-api"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "dev"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-api"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-dev-poker-api-container"
              SECRET_NAME    = "/dev/app/coinpoker/poker-api/env"
              VERSION_TYPE   = "dev"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_poker_api.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-poker-api"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    poker-init = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker poker-init dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-poker-api"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "dev"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-poker-init"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-dev-poker-init-container"
              SECRET_NAME    = "/dev/app/coinpoker/poker-init/env"
              VERSION_TYPE   = "dev"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_poker_api.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-poker-init"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    backend-public = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker backend-public dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-backend-public"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT                = "dev"
              ECR_REPO                   = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-backend-public"
              AWS_ACCOUNT_ID             = "************"
              SERVICE_NAME               = "coinpoker-dev-backend-public-container"
              SECRET_NAME                = "/dev/app/coinpoker/backend-public/env"
              PLAYER_SESSION_SECRET_NAME = "/dev/app/coinpoker/backend-public/player_session_public.pem"
              CONFIG_SECRET_NAME         = "/dev/app/coinpoker/backend-public/config.json"
              VERSION_TYPE               = "dev"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_backend_public.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-backend-public"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    ofcp-backoffice = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker ofcp-backoffice dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-php-ofcp-backoffice"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "dev"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-ofcp-backoffice"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-dev-ofcp-backoffice-container"
              SECRET_NAME    = "/dev/app/coinpoker/php-ofcp-backoffice/backoffice.conf"
              VERSION_TYPE   = "dev"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_php_ofcp_backoffice.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-ofcp-backoffice"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    backoffice-mt = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker backoffice-mt dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "backoffice-multitenant-poc"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "dev"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-backoffice-mt"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-dev-backoffice-mt-container"
              SECRET_NAME    = "/dev/app/coinpoker/backoffice-multitenant/env"
              VERSION_TYPE   = "dev"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_backoffice_multitenant.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-backoffice-mt"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },



    #### STAGING
    backend-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-backend"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT                = "stg"
              ECR_REPO                   = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-backend"
              AWS_ACCOUNT_ID             = "************"
              SERVICE_NAME               = "coinpoker-stg-backend-container"
              SECRET_NAME                = "/stg/app/coinpoker/backend/env-vars"
              PLAYER_SESSION_SECRET_NAME = "/stg/app/coinpoker/backend/player_session_public.pem"
              CONFIG_SECRET_NAME         = "/stg/app/coinpoker/backend/config.json"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_backend.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-backend"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    update-stg = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-web-client"
            branch = "stg"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "stg"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-update"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-stg-update-container"
              SECRET_NAME    = "/dev/app/coinpoker/update/env-vars"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_update.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-update"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    auth-session = {
      environment = "dev"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "dev"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker dev environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-auth-session-signer"
            branch = "develop"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "dev"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-dev-auth-session"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-dev-auth-session-container"
              SECRET_NAME    = "/dev/app/coinpoker/auth-session/env-vars"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_auth_session.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-dev-auth-session"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    frontend-s = {
      name        = "frontend"
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinfuture"
        Purpose     = "Pipeline for Frontend static web in Coinpoker stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-frontend"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:7.0"
            environment_variables = {
              ENVIRONMENT        = "stg"
              SECRET_NAME        = "/stg/app/coinpoker/frontend/env"
              CONFIG_SECRET_NAME = "/stg/app/coinpoker/frontend/appconfig.json"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_frontend.yml"
          }
        },
        {
          name = "DeployStaticWeb"
          type = "deploy"
          deploy_config = {
            deployment_type  = "S3" # Use S3 as provider
            compute_platform = "S3" # Use S3 as compute platform
            configuration = {
              BucketName = "coinpoker-stg-frontend"
              Extract    = "true" # If your artifacts are zipped
            }
          }
          depends_on_stages = ["Build"]
        },
        {
          name = "InvalidateCache"
          type = "aws_cli"
          aws_cli_config = {
            commands = [
              "aws cloudfront create-invalidation --distribution-id $DISTRIBUTION_ID --paths \"/*\""
            ]
            environment_variables = {
              DISTRIBUTION_ID = "E29C8L3NNZGZI1"
            }
          }
          depends_on_stages = ["DeployStaticWeb"]
        }
      ]
    },
    update-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-web-client"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "stg"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-update"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-stg-update-container"
              SECRET_NAME    = "/dev/app/coinpoker/update/env-vars"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_update.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-update"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    auth-session-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-auth-session-signer"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "stg"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-auth-session"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-stg-auth-session-container"
              SECRET_NAME    = "/stg/app/coinpoker/auth-session/env-vars"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_auth_session.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-auth-session"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    third-par-libs-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker third-party-libs stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-third-party-libs"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "stg"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-third-party-libs"
              AWS_ACCOUNT_ID = "************"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_third_party_libs.yml"
          }
        },
        {
          name = "TriggerDependenciesPipeline"
          type = "aws_cli"
          aws_cli_config = {
            commands = [
              "aws codepipeline start-pipeline-execution --name $TRIGGER_PIPELINE"
            ]
            environment_variables = {
              TRIGGER_PIPELINE = "coinpoker-stg-dependencies-s-pipeline"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    dependencies-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker dependencies stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-dependencies"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT           = "stg"
              ECR_REPO              = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-dependencies"
              AWS_ACCOUNT_ID        = "************"
              DOWNLOAD_DBDUMP       = "/stg/app/coinpoker/dependencies/download_dbdump.sh"
              THIRD_PARTY_LIBS_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-third-party-libs"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_dependencies.yml"
          }
        },
        {
          name = "TriggerProviderServicesPipeline"
          type = "aws_cli"
          aws_cli_config = {
            commands = [
              "aws codepipeline start-pipeline-execution --name $TRIGGER_PIPELINE"
            ]
            environment_variables = {
              TRIGGER_PIPELINE = "coinpoker-stg-provider-servi-s-pipeline"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    provider-servi-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker provider-services stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-provider-services"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_SMALL"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "stg"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-provider-services"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-dependencies"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_provider_services.yml"
          }
        }
      ]
    },
    proxy-server-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker proxy-server stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-proxy"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "stg"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-proxy-server"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-dependencies"
              SERVICE_NAME      = "coinpoker-stg-proxy-server-container"
              SECRET_NAME       = "/stg/app/coinpoker/proxy-server/modules.conf"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_proxy_server.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-proxy-server"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    accounting-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker accounting stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-accounting"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT            = "stg"
              ECR_REPO               = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-accounting"
              AWS_ACCOUNT_ID         = "************"
              DEPENDENCIES_REPO      = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-dependencies"
              PROVIDER_SERVICES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-provider-services"
              SERVICE_NAME           = "coinpoker-stg-accounting-container"
              SECRET_NAME            = "/stg/app/coinpoker/accounting/modules.conf"
              DOWNLOAD_DBDUMP        = "/stg/app/coinpoker/dependencies/download_dbdump.sh"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_accounting.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-accounting"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    authentication-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker authentication stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-authentication"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT            = "stg"
              ECR_REPO               = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-authentication"
              AWS_ACCOUNT_ID         = "************"
              DEPENDENCIES_REPO      = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-dependencies"
              PROVIDER_SERVICES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-provider-services"
              SERVICE_NAME           = "coinpoker-stg-authentication-container"
              SECRET_NAME            = "/stg/app/coinpoker/authentication/modules.conf"
              DOWNLOAD_DBDUMP        = "/stg/app/coinpoker/dependencies/download_dbdump.sh"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_authentication.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-authentication"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    poker-operation-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker poker-operation stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-poker-operation"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "stg"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-operation"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-dependencies"
              SERVICE_NAME      = "coinpoker-stg-poker-operation-container"
              SECRET_NAME       = "/stg/app/coinpoker/poker-operation/modules.conf"
              VERSION_TYPE      = "staging"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_poker_operation.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-poker-operation"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    poker-lobby-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker poker-lobby stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-poker-lobby"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "stg"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-lobby"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-dependencies"
              SERVICE_NAME      = "coinpoker-stg-poker-lobby-container"
              SECRET_NAME       = "/stg/app/coinpoker/poker-lobby/modules.conf"
              VERSION_TYPE      = "staging"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_poker_lobby.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-poker-lobby"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    server-lobby-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker server-lobby stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-lobby"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "stg"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-server-lobby"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-dependencies"
              SERVICE_NAME      = "coinpoker-stg-server-lobby-container"
              SECRET_NAME       = "/stg/app/coinpoker/server-lobby/modules.conf"
              VERSION_TYPE      = "stagig"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_server_lobby.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-server-lobby"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    server-stats-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker server-statistics stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-statistics"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "stg"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-server-statistics"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-dependencies"
              SERVICE_NAME      = "coinpoker-stg-server-statistics-container"
              SECRET_NAME       = "/stg/app/coinpoker/server-statistics/modules.conf"
              VERSION_TYPE      = "staging"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_server_statistics.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-server-statistics"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    poker-history-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker poker-history stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-poker-history"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "stg"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-history"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-dependencies"
              SERVICE_NAME      = "coinpoker-stg-poker-history-container"
              SECRET_NAME       = "/stg/app/coinpoker/server-poker-history/modules.conf"
              VERSION_TYPE      = "staging"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_server_poker_history.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-poker-history"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    server-chat-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker server-chat stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-chat"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT       = "stg"
              ECR_REPO          = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-server-chat"
              AWS_ACCOUNT_ID    = "************"
              DEPENDENCIES_REPO = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-dependencies"
              SERVICE_NAME      = "coinpoker-stg-server-chat-container"
              SECRET_NAME       = "/stg/app/coinpoker/server-chat/modules.conf"
              VERSION_TYPE      = "staging"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_server_chat.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-server-chat"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    customerio-pub-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker customerio-pub stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-customerio-publisher"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "stg"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-customerio-pub"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-stg-customerio-pub-container"
              SECRET_NAME    = "/stg/app/coinpoker/customerio-publisher/cio.conf"
              VERSION_TYPE   = "staging"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_customerio_publisher.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-customerio-pub"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    poker-api-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker poker-api stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-poker-api"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "stg"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-api"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-stg-poker-api-container"
              SECRET_NAME    = "/stg/app/coinpoker/poker-api/env"
              VERSION_TYPE   = "staging"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_poker_api.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-poker-api"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    poker-init-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker poker-init stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-server-poker-api"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "stg"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-poker-init"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-stg-poker-init-container"
              SECRET_NAME    = "/stg/app/coinpoker/poker-init/env"
              VERSION_TYPE   = "staging"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_poker_api.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-poker-init"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    backend-public-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker backend-public stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-backend-public"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT                = "stg"
              ECR_REPO                   = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-backend-public"
              AWS_ACCOUNT_ID             = "************"
              SERVICE_NAME               = "coinpoker-stg-backend-public-container"
              SECRET_NAME                = "/stg/app/coinpoker/backend-public/env"
              PLAYER_SESSION_SECRET_NAME = "/stg/app/coinpoker/backend-public/player_session_public.pem"
              CONFIG_SECRET_NAME         = "/stg/app/coinpoker/backend-public/config.json"
              VERSION_TYPE               = "staging"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_backend_public.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-backend-public"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    },
    ofcp-backoffice-s = {
      environment = "stg"
      project     = "coinpoker"
      vpc         = "main"
      tags = {
        Environment = "stg"
        Product     = "coinpoker"
        Purpose     = "Pipeline for Coinpoker ofcp-backoffice stg environment"
      }
      sources = {
        main = {
          provider       = "github"
          connection_arn = "arn:aws:codeconnections:eu-west-1:************:connection/9efb517d-3e65-40cd-9846-08034e1b17e7"
          configuration = {
            owner  = "BlockLabsDev"
            repo   = "coinpoker-php-ofcp-backoffice"
            branch = "staging"
          }
        }
      }
      stages = [
        {
          name = "Build"
          type = "build"
          build_config = {
            compute_type = "BUILD_GENERAL1_MEDIUM"
            alias        = "build"
            image        = "aws/codebuild/standard:5.0"
            environment_variables = {
              ENVIRONMENT    = "stg"
              ECR_REPO       = "************.dkr.ecr.eu-west-1.amazonaws.com/coinpoker-stg-ofcp-backoffice"
              AWS_ACCOUNT_ID = "************"
              SERVICE_NAME   = "coinpoker-stg-ofcp-backoffice-container"
              SECRET_NAME    = "/stg/app/coinpoker/php-ofcp-backoffice/backoffice.conf"
              VERSION_TYPE   = "staging"
            }
            buildspec = "accounts/buildspec/applications/buildspec_coinpoker_php_ofcp_backoffice.yml"
          }
        },
        {
          name = "Deploy"
          type = "deploy"
          deploy_config = {
            compute_platform = "ECS"
            deployment_type  = "ECS"
            configuration = {
              ClusterName = "coinpoker-shared-ecs"
              ServiceName = "coinpoker-stg-ofcp-backoffice"
              FileName    = "images.json"
            }
          }
          depends_on_stages = ["Build"]
        }
      ]
    }
  }
}

variable "coinpoker_rabbitmq_configs" {
  type = map(object({
    vpc                 = string
    environment         = string
    is_shared           = bool
    instance_type       = string
    engine_version      = string
    admin_username      = string
    deployment_mode     = string
    publicly_accessible = bool
    allowed_cidr_blocks = list(string)
    tags                = map(string)
  }))

  default = {
    main = {
      vpc                 = "main"
      environment         = ""
      is_shared           = true
      instance_type       = "mq.t3.micro"
      engine_version      = "3.13"
      deployment_mode     = "SINGLE_INSTANCE"
      admin_username      = "coinpoker"
      publicly_accessible = false
      allowed_cidr_blocks = ["********/16", "10.10.0.0/16"]
      tags = {
        Name        = "rabbitmq"
        Environment = "shared"
      }
    },
    staging = {
      vpc                 = "main"
      environment         = "staging"
      is_shared           = false
      instance_type       = "mq.t3.micro"
      engine_version      = "3.13"
      deployment_mode     = "SINGLE_INSTANCE"
      admin_username      = "coinpoker"
      publicly_accessible = false
      allowed_cidr_blocks = ["********/16", "10.10.0.0/16"]
      tags = {
        Name        = "rabbitmq"
        Environment = "staging"
      }
    }
  }
}
